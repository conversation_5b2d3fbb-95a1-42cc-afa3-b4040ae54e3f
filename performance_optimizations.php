<?php
/**
 * تحسينات الأداء والاستجابة لنظام إدارة المهام
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

/**
 * فئة تحسين الأداء
 */
class PerformanceOptimizer {
    
    private $cacheDir;
    private $cacheExpiry;
    private $compressionEnabled;
    
    public function __construct($cacheDir = 'cache/', $cacheExpiry = 3600) {
        $this->cacheDir = $cacheDir;
        $this->cacheExpiry = $cacheExpiry;
        $this->compressionEnabled = extension_loaded('zlib');
        
        // إنشاء مجلد التخزين المؤقت إذا لم يكن موجوداً
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * تفعيل ضغط الإخراج
     */
    public function enableCompression() {
        if ($this->compressionEnabled && !ob_get_level()) {
            ob_start('ob_gzhandler');
        }
    }
    
    /**
     * تعيين headers للتخزين المؤقت
     */
    public function setCacheHeaders($maxAge = 3600) {
        $expires = gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT';
        
        header("Cache-Control: public, max-age={$maxAge}");
        header("Expires: {$expires}");
        header("Last-Modified: " . gmdate('D, d M Y H:i:s', time()) . ' GMT');
        header("ETag: " . md5($_SERVER['REQUEST_URI']));
    }
    
    /**
     * تخزين البيانات في الذاكرة المؤقتة
     */
    public function setCache($key, $data, $expiry = null) {
        $expiry = $expiry ?: $this->cacheExpiry;
        $cacheFile = $this->cacheDir . md5($key) . '.cache';
        
        $cacheData = [
            'data' => $data,
            'expiry' => time() + $expiry,
            'created' => time()
        ];
        
        file_put_contents($cacheFile, serialize($cacheData), LOCK_EX);
    }
    
    /**
     * استرجاع البيانات من الذاكرة المؤقتة
     */
    public function getCache($key) {
        $cacheFile = $this->cacheDir . md5($key) . '.cache';
        
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $cacheData = unserialize(file_get_contents($cacheFile));
        
        if (time() > $cacheData['expiry']) {
            unlink($cacheFile);
            return false;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * مسح الذاكرة المؤقتة
     */
    public function clearCache($pattern = '*') {
        $files = glob($this->cacheDir . $pattern . '.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    /**
     * تحسين استعلامات قاعدة البيانات
     */
    public function optimizeQuery($conn, $sql, $params = []) {
        // إنشاء مفتاح فريد للاستعلام
        $queryKey = 'query_' . md5($sql . serialize($params));
        
        // محاولة الحصول على النتيجة من الذاكرة المؤقتة
        $cachedResult = $this->getCache($queryKey);
        if ($cachedResult !== false) {
            return $cachedResult;
        }
        
        // تنفيذ الاستعلام
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تخزين النتيجة في الذاكرة المؤقتة
        $this->setCache($queryKey, $result, 1800); // 30 دقيقة
        
        return $result;
    }
    
    /**
     * تحميل الصور بشكل تدريجي
     */
    public function generateLazyLoadHTML($src, $alt = '', $class = '') {
        return '<img data-src="' . htmlspecialchars($src) . '" 
                     alt="' . htmlspecialchars($alt) . '" 
                     class="lazy ' . htmlspecialchars($class) . '" 
                     src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E">';
    }
    
    /**
     * ضغط CSS
     */
    public function minifyCSS($css) {
        // إزالة التعليقات
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // إزالة المسافات الزائدة
        $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
        
        // إزالة المسافات حول الرموز
        $css = str_replace(['; ', ' ;', ' {', '{ ', '} ', ' }', ': ', ' :', ', ', ' ,'], 
                          [';', ';', '{', '{', '}', '}', ':', ':', ',', ','], $css);
        
        return trim($css);
    }
    
    /**
     * ضغط JavaScript
     */
    public function minifyJS($js) {
        // إزالة التعليقات أحادية السطر
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // إزالة التعليقات متعددة الأسطر
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // إزالة المسافات الزائدة
        $js = preg_replace('/\s+/', ' ', $js);
        
        // إزالة المسافات حول الرموز
        $js = str_replace([' = ', ' + ', ' - ', ' * ', ' / ', ' == ', ' != ', ' && ', ' || '], 
                         ['=', '+', '-', '*', '/', '==', '!=', '&&', '||'], $js);
        
        return trim($js);
    }
    
    /**
     * تحسين الصور
     */
    public function optimizeImage($imagePath, $quality = 85) {
        if (!file_exists($imagePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($imagePath);
        $mimeType = $imageInfo['mime'];
        
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                imagejpeg($image, $imagePath, $quality);
                break;
                
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                imagepng($image, $imagePath, 9);
                break;
                
            case 'image/gif':
                // GIF لا يحتاج ضغط إضافي
                break;
        }
        
        if (isset($image)) {
            imagedestroy($image);
        }
        
        return true;
    }
    
    /**
     * تجميع ملفات CSS
     */
    public function combineCSS($files, $outputFile) {
        $combinedCSS = '';
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $css = file_get_contents($file);
                $combinedCSS .= $this->minifyCSS($css) . "\n";
            }
        }
        
        file_put_contents($outputFile, $combinedCSS);
        return $outputFile;
    }
    
    /**
     * تجميع ملفات JavaScript
     */
    public function combineJS($files, $outputFile) {
        $combinedJS = '';
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $js = file_get_contents($file);
                $combinedJS .= $this->minifyJS($js) . ";\n";
            }
        }
        
        file_put_contents($outputFile, $combinedJS);
        return $outputFile;
    }
    
    /**
     * مراقبة الأداء
     */
    public function startPerformanceMonitoring() {
        return [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true)
        ];
    }
    
    /**
     * إنهاء مراقبة الأداء
     */
    public function endPerformanceMonitoring($startMetrics) {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);
        
        return [
            'execution_time' => round(($endTime - $startMetrics['start_time']) * 1000, 2), // بالميلي ثانية
            'memory_used' => $this->formatBytes($endMemory - $startMetrics['start_memory']),
            'peak_memory' => $this->formatBytes($endPeakMemory),
            'total_memory' => $this->formatBytes($endMemory)
        ];
    }
    
    /**
     * تنسيق حجم الذاكرة
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * تحسين قاعدة البيانات
     */
    public function optimizeDatabase($conn) {
        $optimizations = [];
        
        try {
            // تحليل الجداول
            $tables = ['notifications', 'task_comments', 'task_history', 'task_categories'];
            
            foreach ($tables as $table) {
                $stmt = $conn->prepare("ANALYZE TABLE {$table}");
                $stmt->execute();
                $optimizations[] = "تم تحليل جدول {$table}";
            }
            
            // تحسين الجداول
            foreach ($tables as $table) {
                $stmt = $conn->prepare("OPTIMIZE TABLE {$table}");
                $stmt->execute();
                $optimizations[] = "تم تحسين جدول {$table}";
            }
            
        } catch (Exception $e) {
            $optimizations[] = "خطأ في التحسين: " . $e->getMessage();
        }
        
        return $optimizations;
    }
    
    /**
     * إنشاء Service Worker للتخزين المؤقت
     */
    public function generateServiceWorker($cacheVersion = '1.0') {
        $sw = "
const CACHE_NAME = 'task-management-v{$cacheVersion}';
const urlsToCache = [
    '/',
    '/task-management-style.css',
    '/task-management-script.js',
    '/bootstrap.min.css',
    '/fontawesome.min.css'
];

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
";
        
        file_put_contents('sw.js', $sw);
        return 'sw.js';
    }
    
    /**
     * تحسين الاستعلامات الشائعة
     */
    public function getOptimizedTasksQuery($filters = []) {
        $sql = "SELECT 
            n.ID, n.Title, n.Message, n.NotifyDate, n.ToDate, n.Status,
            n.Priority, n.Category, n.Progress, n.CreatedAt,
            u1.FullName as CreatedByName,
            u2.FullName as AssignedToName,
            tc.Color as CategoryColor, tc.Icon as CategoryIcon
        FROM notifications n
        USE INDEX (idx_status_priority, idx_notify_date)
        LEFT JOIN users u1 ON n.CreatedBy = u1.ID
        LEFT JOIN users u2 ON n.AssignedTo = u2.ID
        LEFT JOIN task_categories tc ON n.Category = tc.Name";
        
        $conditions = [];
        $params = [];
        
        if (!empty($filters['status'])) {
            $conditions[] = "n.Status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['priority'])) {
            $conditions[] = "n.Priority = ?";
            $params[] = $filters['priority'];
        }
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $sql .= " ORDER BY 
            CASE n.Priority 
                WHEN 'عاجلة' THEN 1 
                WHEN 'عالية' THEN 2 
                WHEN 'متوسطة' THEN 3 
                WHEN 'منخفضة' THEN 4 
            END,
            n.NotifyDate ASC
            LIMIT 50";
        
        return ['sql' => $sql, 'params' => $params];
    }
}

/**
 * فئة مراقبة الأداء في الوقت الفعلي
 */
class RealTimePerformanceMonitor {
    
    private $metrics = [];
    
    public function startTimer($name) {
        $this->metrics[$name] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage(true)
        ];
    }
    
    public function endTimer($name) {
        if (!isset($this->metrics[$name])) {
            return false;
        }
        
        $this->metrics[$name]['end'] = microtime(true);
        $this->metrics[$name]['memory_end'] = memory_get_usage(true);
        $this->metrics[$name]['duration'] = $this->metrics[$name]['end'] - $this->metrics[$name]['start'];
        $this->metrics[$name]['memory_used'] = $this->metrics[$name]['memory_end'] - $this->metrics[$name]['memory_start'];
        
        return $this->metrics[$name];
    }
    
    public function getMetrics() {
        return $this->metrics;
    }
    
    public function logSlowQueries($threshold = 1.0) {
        foreach ($this->metrics as $name => $metric) {
            if (isset($metric['duration']) && $metric['duration'] > $threshold) {
                error_log("Slow operation detected: {$name} took {$metric['duration']} seconds");
            }
        }
    }
}

// إنشاء مثيل عام لمحسن الأداء
$performanceOptimizer = new PerformanceOptimizer();
$performanceMonitor = new RealTimePerformanceMonitor();

// تفعيل الضغط
$performanceOptimizer->enableCompression();
?>
