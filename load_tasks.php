<?php
/**
 * تحميل المهام بشكل تدريجي (Infinite Scroll)
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    // الحصول على المعاملات
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // فلاتر إضافية
    $filters = [
        'search' => trim($_GET['search'] ?? ''),
        'status' => $_GET['status'] ?? '',
        'priority' => $_GET['priority'] ?? '',
        'category' => $_GET['category'] ?? '',
        'assigned_to' => $_GET['assigned_to'] ?? '',
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? ''
    ];
    
    // بناء الاستعلام
    $query = buildTaskQuery($filters, $limit, $offset);
    
    // تنفيذ الاستعلام
    $stmt = $conn->prepare($query['sql']);
    $stmt->execute($query['params']);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب العدد الإجمالي
    $totalCount = getTotalTaskCount($conn, $filters);
    $totalPages = ceil($totalCount / $limit);
    
    // تنسيق المهام
    $formattedTasks = array_map('formatTaskForDisplay', $tasks);
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'tasks' => $formattedTasks,
        'page' => $page,
        'limit' => $limit,
        'total' => $totalCount,
        'totalPages' => $totalPages,
        'hasMore' => $page < $totalPages
    ]);

} catch (Exception $e) {
    error_log("خطأ في تحميل المهام: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في تحميل المهام',
        'error' => $e->getMessage()
    ]);
}

/**
 * بناء استعلام المهام
 */
function buildTaskQuery($filters, $limit, $offset) {
    $sql = "SELECT 
        n.ID, n.Title, n.Message, n.NotifyDate, n.ToDate, n.Status,
        n.Priority, n.Category, n.Progress, n.Notes, n.CreatedAt,
        u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
        u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
        tc.Color as CategoryColor, tc.Icon as CategoryIcon,
        
        -- حسابات الحالة
        CASE
            WHEN n.Status = 'تم' THEN 'completed'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 'overdue'
            WHEN n.NotifyDate = CURDATE() THEN 'today'
            ELSE 'normal'
        END as row_class,
        
        -- الأيام المتبقية
        CASE
            WHEN n.Status = 'تم' THEN 'مكتمل'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN CONCAT('متأخر ', DATEDIFF(CURDATE(), n.ToDate), ' يوم')
            WHEN n.ToDate = CURDATE() THEN 'ينتهي اليوم'
            WHEN n.ToDate > CURDATE() THEN CONCAT(DATEDIFF(n.ToDate, CURDATE()), ' يوم متبقي')
            ELSE ''
        END as days_remaining,
        
        -- عدد التعليقات
        (SELECT COUNT(*) FROM task_comments tc WHERE tc.TaskID = n.ID) as comments_count
        
    FROM notifications n
    LEFT JOIN users u1 ON n.CreatedBy = u1.ID
    LEFT JOIN users u2 ON n.AssignedTo = u2.ID
    LEFT JOIN task_categories tc ON n.Category = tc.Name";
    
    $conditions = [];
    $params = [];
    
    // فلتر البحث
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    // فلتر الحالة
    if (!empty($filters['status'])) {
        $conditions[] = "n.Status = ?";
        $params[] = $filters['status'];
    }
    
    // فلتر الأولوية
    if (!empty($filters['priority'])) {
        $conditions[] = "n.Priority = ?";
        $params[] = $filters['priority'];
    }
    
    // فلتر التصنيف
    if (!empty($filters['category'])) {
        $conditions[] = "n.Category = ?";
        $params[] = $filters['category'];
    }
    
    // فلتر المسؤول
    if (!empty($filters['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $params[] = $filters['assigned_to'];
    }
    
    // فلاتر التاريخ
    if (!empty($filters['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $params[] = $filters['date_to'];
    }
    
    // إضافة الشروط
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    // الترتيب
    $sql .= " ORDER BY 
        CASE 
            WHEN n.Status = 'تم' THEN 3
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 1
            WHEN n.NotifyDate = CURDATE() THEN 2
            ELSE 4
        END,
        n.Priority DESC,
        n.NotifyDate ASC";
    
    // الحد والإزاحة
    $sql .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return [
        'sql' => $sql,
        'params' => $params
    ];
}

/**
 * حساب العدد الإجمالي للمهام
 */
function getTotalTaskCount($conn, $filters) {
    $sql = "SELECT COUNT(*) as total FROM notifications n";
    
    $conditions = [];
    $params = [];
    
    // نفس شروط الفلترة
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($filters['status'])) {
        $conditions[] = "n.Status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['priority'])) {
        $conditions[] = "n.Priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['category'])) {
        $conditions[] = "n.Category = ?";
        $params[] = $filters['category'];
    }
    
    if (!empty($filters['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $params[] = $filters['assigned_to'];
    }
    
    if (!empty($filters['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $params[] = $filters['date_to'];
    }
    
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return intval($result['total']);
}

/**
 * تنسيق المهمة للعرض
 */
function formatTaskForDisplay($task) {
    // تنسيق التواريخ
    $task['NotifyDate_formatted'] = date('d/m/Y', strtotime($task['NotifyDate']));
    $task['ToDate_formatted'] = $task['ToDate'] ? date('d/m/Y', strtotime($task['ToDate'])) : '';
    $task['CreatedAt_formatted'] = date('d/m/Y H:i', strtotime($task['CreatedAt']));
    
    // تنسيق الأولوية
    $priorityConfig = [
        'منخفضة' => ['class' => 'priority-low', 'icon' => 'fas fa-arrow-down', 'color' => '#28a745'],
        'متوسطة' => ['class' => 'priority-medium', 'icon' => 'fas fa-minus', 'color' => '#ffc107'],
        'عالية' => ['class' => 'priority-high', 'icon' => 'fas fa-arrow-up', 'color' => '#fd7e14'],
        'عاجلة' => ['class' => 'priority-urgent', 'icon' => 'fas fa-exclamation-triangle', 'color' => '#dc3545']
    ];
    
    $priority = $priorityConfig[$task['Priority']] ?? $priorityConfig['متوسطة'];
    $task['priority_class'] = $priority['class'];
    $task['priority_icon'] = $priority['icon'];
    $task['priority_color'] = $priority['color'];
    
    // تنسيق الحالة
    $statusConfig = [
        'تم' => ['class' => 'status-completed', 'icon' => 'fas fa-check-circle', 'color' => '#28a745'],
        'تحت التنفيذ' => ['class' => 'status-in-progress', 'icon' => 'fas fa-clock', 'color' => '#ffc107'],
        'لم تنفذ' => ['class' => 'status-pending', 'icon' => 'fas fa-hourglass-half', 'color' => '#6c757d'],
        'ملغي' => ['class' => 'status-cancelled', 'icon' => 'fas fa-times-circle', 'color' => '#dc3545']
    ];
    
    $status = $statusConfig[$task['Status']] ?? $statusConfig['لم تنفذ'];
    $task['status_class'] = $status['class'];
    $task['status_icon'] = $status['icon'];
    $task['status_color'] = $status['color'];
    
    // تنسيق التقدم
    $progress = intval($task['Progress']);
    $task['progress_class'] = getProgressClass($progress);
    $task['progress_width'] = $progress . '%';
    
    // تقصير النصوص الطويلة
    $task['Title_short'] = mb_substr($task['Title'], 0, 60) . (mb_strlen($task['Title']) > 60 ? '...' : '');
    $task['Message_short'] = mb_substr(strip_tags($task['Message']), 0, 120) . (mb_strlen($task['Message']) > 120 ? '...' : '');
    
    // معلومات إضافية
    $task['has_comments'] = $task['comments_count'] > 0;
    $task['is_overdue'] = $task['row_class'] === 'overdue';
    $task['is_today'] = $task['row_class'] === 'today';
    $task['is_completed'] = $task['Status'] === 'تم';
    
    // تنسيق التصنيف
    if ($task['Category']) {
        $task['category_badge'] = [
            'name' => $task['Category'],
            'color' => $task['CategoryColor'] ?? '#007bff',
            'icon' => $task['CategoryIcon'] ?? 'fas fa-folder'
        ];
    }
    
    // تنسيق المستخدمين
    $task['created_by_display'] = $task['CreatedByFullName'] ?? $task['CreatedByName'] ?? 'غير محدد';
    $task['assigned_to_display'] = $task['AssignedToFullName'] ?? $task['AssignedToName'] ?? 'غير محدد';
    
    return $task;
}

/**
 * الحصول على كلاس التقدم
 */
function getProgressClass($progress) {
    if ($progress >= 80) return 'progress-high';
    if ($progress >= 50) return 'progress-medium';
    if ($progress >= 20) return 'progress-low';
    return 'progress-none';
}

/**
 * إنشاء HTML للمهمة
 */
function generateTaskHTML($task) {
    $html = '<tr class="task-row ' . $task['row_class'] . '" data-task-id="' . $task['ID'] . '">';
    
    // العنوان
    $html .= '<td class="task-title">';
    $html .= '<div class="d-flex align-items-center">';
    $html .= '<i class="' . $task['status_icon'] . ' me-2" style="color: ' . $task['status_color'] . ';"></i>';
    $html .= '<span title="' . htmlspecialchars($task['Title']) . '">' . htmlspecialchars($task['Title_short']) . '</span>';
    $html .= '</div>';
    $html .= '</td>';
    
    // الوصف
    $html .= '<td class="task-description">';
    $html .= '<span title="' . htmlspecialchars(strip_tags($task['Message'])) . '">' . htmlspecialchars($task['Message_short']) . '</span>';
    $html .= '</td>';
    
    // التاريخ
    $html .= '<td>' . $task['NotifyDate_formatted'] . '</td>';
    $html .= '<td>' . $task['ToDate_formatted'] . '</td>';
    
    // الحالة
    $html .= '<td>';
    $html .= '<span class="status-badge ' . $task['status_class'] . '" data-status="' . $task['Status'] . '">';
    $html .= '<i class="' . $task['status_icon'] . '"></i> ' . $task['Status'];
    $html .= '</span>';
    $html .= '</td>';
    
    // الأولوية
    $html .= '<td>';
    $html .= '<span class="priority-badge ' . $task['priority_class'] . '" data-priority="' . $task['Priority'] . '">';
    $html .= '<i class="' . $task['priority_icon'] . '"></i> ' . $task['Priority'];
    $html .= '</span>';
    $html .= '</td>';
    
    // التقدم
    $html .= '<td>';
    $html .= '<div class="progress-container">';
    $html .= '<div class="progress-bar ' . $task['progress_class'] . '" style="width: ' . $task['progress_width'] . '"></div>';
    $html .= '</div>';
    $html .= '<small class="text-muted">' . $task['Progress'] . '%</small>';
    $html .= '</td>';
    
    // الإجراءات
    $html .= '<td class="action-buttons">';
    if ($task['Status'] !== 'تم') {
        $html .= '<button class="btn btn-sm btn-success action-btn" data-action="complete" title="إكمال">';
        $html .= '<i class="fas fa-check"></i>';
        $html .= '</button>';
    }
    $html .= '<button class="btn btn-sm btn-primary action-btn" data-action="edit" title="تعديل">';
    $html .= '<i class="fas fa-edit"></i>';
    $html .= '</button>';
    $html .= '<button class="btn btn-sm btn-danger action-btn" data-action="delete" title="حذف">';
    $html .= '<i class="fas fa-trash"></i>';
    $html .= '</button>';
    $html .= '</td>';
    
    $html .= '</tr>';
    
    return $html;
}
?>
