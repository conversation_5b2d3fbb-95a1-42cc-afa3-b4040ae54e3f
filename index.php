<?php
require 'db.php'; // الاتصال بقاعدة البيانات
require_once 'functions.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['user_id'])) {
    $_SESSION['page_start_time'] = time();

    $userId = $_SESSION['user_id'];
    $page = basename($_SERVER['PHP_SELF']);
    $action = 'زيارة الصفحة';
    $url = $_SERVER['REQUEST_URI'];
    $method = $_SERVER['REQUEST_METHOD'];
    $data = ($method === 'POST') ? json_encode($_POST) : json_encode($_GET);
    $duration = time() - $_SESSION['page_start_time'];
    logActivity($conn, $userId, $page, $action, $url, $method, $data, $duration);
}

// تحقق مما إذا كانت الجلسة قد بدأت
if (isset($_SESSION['last_activity'])) {
    // احسب الوقت المنقضي منذ آخر نشاط
    $inactive_time = time() - $_SESSION['last_activity'];

    // إذا تجاوز الوقت غير النشط 5 دقائق (300 ثانية)، قم بإنهاء الجلسة
    if ($inactive_time > 300) {
        // إنهاء الجلسة
        session_unset();
        session_destroy();

        // إعادة التوجيه إلى صفحة تسجيل الدخول مع رسالة
        header("Location: /e-finance/login?message=session_expired");
        exit;
    }
}

// تحديث وقت آخر نشاط
$_SESSION['last_activity'] = time();

// تحقق من وجود المستخدم في الجلسة
if (!isset($_SESSION['user_id'])) {
    // إذا لم يكن المستخدم مسجل الدخول، قم بإعادة التوجيه إلى صفحة تسجيل الدخول
    header("Location: /e-finance/login");
    exit;
}


// جلب التاريخ المطلوب من الرابط أو استخدام تاريخ اليوم
$selectedDate = $_GET['date'] ?? date('Y-m-d');
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

$notifications = $conn->prepare("SELECT * FROM Notifications WHERE NotifyDate = ?");
$notifications->execute([$date]);

// جلب التنبيهات حسب التاريخ والحالة
$pendingStmt = $conn->prepare("
    SELECT * FROM Notifications 
    WHERE NotifyDate = :date 
    AND (Status = 'لم تنفذ' OR Status = 'تحت التنفيذ')
    ORDER BY NotifyDate DESC
");
$pendingStmt->bindParam(':date', $selectedDate);
$pendingStmt->execute();
$pendingAlerts = $pendingStmt->fetchAll(PDO::FETCH_ASSOC);

// لحساب الأيام السابقة والتالية
$prevDate = date('Y-m-d', strtotime($date . ' -1 day'));
$nextDate = date('Y-m-d', strtotime($date . ' +1 day'));

// استدعاء اسم المستخدم
$userID = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM Users WHERE ID = :userID");
$stmt->bindParam(':userID', $userID, PDO::PARAM_INT);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// التحقق من وجود بيانات المستخدم
if ($user) {
    $username = $user['Username'];
    $role = $user['Role'];
} else {
    // في حالة عدم وجود المستخدم، إعادة توجيه لصفحة تسجيل الدخول
    session_destroy();
    header("Location: /e-finance/login");
    exit;
}


try {
    // جلب إجمالي الموازنة
    $stmt = $conn->prepare("SELECT Total FROM BudgetTotal WHERE ID = 1");
    $stmt->execute();
    $budgetTotal = $stmt->fetchColumn();

    // جلب سجل المبالغ المضافة
    $stmt = $conn->prepare("SELECT * FROM BudgetSupport ORDER BY Date DESC");
    $stmt->execute();
    $supportRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    echo "حدث خطأ أثناء جلب البيانات: " . $e->getMessage();
    exit;
}


$totalsStmt = $conn->prepare("
    SELECT 
        Category,
        SUM(Amount) AS TotalAmount
    FROM Customers
    WHERE StartDate BETWEEN :from AND :to
    GROUP BY Category
");
$from = $_GET['from'] ?? '2000-01-01';
$to = $_GET['to'] ?? date('Y-m-d');
$totalsStmt->bindParam(':from', $from);
$totalsStmt->bindParam(':to', $to);
$totalsStmt->execute();
$categoryTotals = $totalsStmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .hero-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .hero-title {
            color: #000000;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #000000, #333333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .tasks-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .tasks-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .tasks-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .tasks-header h4 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .budget-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .budget-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .budget-total {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .budget-total h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .btn-hover {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            font-weight: 600;
        }

        .btn-hover:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #000000, #333333);
            border: none;
        }

        .table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .table thead th {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table tbody td {
            padding: 12px 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-section {
                padding: 25px;
            }

            .tasks-container,
            .budget-container {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
   </head>
<body>
    <?php include 'header.php'; ?>

    <!-- القسم الرئيسي -->
        <!-- عنوان الصفحة الرئيسية -->
        <div class="hero-section">
            <h1 class="hero-title">
                <i class="fas fa-home me-3"></i>نظام إدارة efinance الإلكتروني
            </h1>
            <p class="hero-subtitle">
                مرحباً بك في النظام المتكامل لإدارة العملاء والمعاملات المالية
            </p>
            <div class="row g-3 justify-content-center">
                <div class="col-md-3">
                    <a href="register.php" class="btn btn-primary btn-hover w-100">
                        <i class="fas fa-user-plus me-2"></i>تسجيل عميل جديد
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="search.php" class="btn btn-success btn-hover w-100">
                        <i class="fas fa-search me-2"></i>البحث في العملاء
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-warning btn-hover w-100">
                        <i class="fas fa-chart-line me-2"></i>التقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- قسم المهام -->
        <div class="tasks-container">
            <div class="tasks-header">
                <h4><i class="fas fa-tasks me-2"></i>المهام اليومية (<?= $selectedDate ?>)</h4>
            </div>
        <div class="card-body">
            <?php if (count($pendingAlerts) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered text-center align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th>العنوان</th>
                                <th>الوصف</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pendingAlerts as $alert): ?>
                                <tr>
                                    <td><?= htmlspecialchars($alert['Title']) ?></td>
                                    <td><?= nl2br(htmlspecialchars($alert['Message'])) ?></td>
                                    <td><?= $alert['NotifyDate'] ?></td>
                                    <td><?= $alert['Status'] ?></td>
                                    <td class="d-flex justify-content-center gap-2 flex-wrap">
                                <form method="post" action="update_status.php" onsubmit="return confirm('هل أنت متأكد من تنفيذ التنبيه؟');">
                                    <input type="hidden" name="id" value="<?= $alert['ID'] ?>">
                                    <input type="hidden" name="action" value="mark_done">
                                    <button type="submit" class="btn btn-success btn-sm">تم التنفيذ</button>
                                </form>
                                <form method="post" action="update_status.php" onsubmit="return confirm('هل تريد ترحيل التنبيه لليوم التالي؟');">
                                    <input type="hidden" name="id" value="<?= $alert['ID'] ?>">
                                    <input type="hidden" name="action" value="postpone">
                                    <button type="submit" class="btn btn-warning btn-sm">ترحيل لليوم التالي</button>
                                </form>
                                </td>

                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted fs-5">لا توجد مهام مؤجلة أو تحت التنفيذ لهذا اليوم</p>
                    <p class="text-muted">جميع المهام مكتملة!</p>
                </div>
            <?php endif; ?>

            <div class="d-flex justify-content-center gap-3 mt-4">
                <a href="?date=<?= $prevDate ?>" class="btn btn-outline-primary btn-hover">
                    <i class="fas fa-chevron-right me-2"></i>اليوم السابق
                </a>
                <a href="?date=<?= $nextDate ?>" class="btn btn-outline-primary btn-hover">
                    اليوم التالي<i class="fas fa-chevron-left ms-2"></i>
                </a>
            </div>
        </div>
        <!-- قسم الموازنات -->
        <div class="budget-container">
            <div class="text-center mb-4">
                <h2 class="text-primary"><i class="fas fa-wallet me-2"></i>أرصدة الموازنات</h2>
            </div>

            <!-- عرض إجمالي الموازنة -->
            <div class="budget-total">
                <h2><i class="fas fa-coins me-2"></i>إجمالي الموازنة الحالية: <?= number_format($budgetTotal, 2) ?> ج.م</h2>
            </div>

            <!-- جدول عرض المبالغ المضافة -->
            <div class="mt-4">
                <h3 class="text-center mb-4"><i class="fas fa-list me-2"></i>سجل المبالغ المضافة</h3>
                <div class="table-responsive">
                    <table class="table table-hover">
            <thead class="table-dark">
                <tr>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                    <th>الشرح</th>
                    <th>الملف</th>
                    <th>الاجراء</th>
                </tr>
            </thead>
            <tbody>
            <?php if (!empty($supportRecords)): ?>
                <?php foreach ($supportRecords as $record): ?>
                    <tr>
                        <td><?= htmlspecialchars($record['Date']) ?></td>
                        <td><?= number_format($record['Amount'], 2) ?> ج.م</td>
                        <td><?= htmlspecialchars($record['Description']) ?></td>
                        <td>
                            <?php if ($record['File']): ?>
                                <a href="uploads/<?= htmlspecialchars($record['File']) ?>" target="_blank">عرض الملف</a>
                            <?php else: ?>
                                لا يوجد
                            <?php endif; ?>
                        </td>
                        <td>
                  <a href="edit_budget.php?id=<?= $record['ID'] ?>" class="btn btn-warning btn-sm">تعديل</a>
                  <a href="delete_budget.php?id=<?= $record['ID'] ?>" onclick="return confirm('هل أنت متأكد من الحذف؟');" class="btn btn-danger btn-sm">حذف</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4" class="text-center">لا توجد مبالغ مضافة حتى الآن.</td>
                </tr>
            <?php endif; ?>
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- زر لإضافة مبلغ جديد -->
            <div class="text-center mt-4">
                <a href="add_budget.php" class="btn btn-primary btn-hover">
                    <i class="fas fa-plus me-2"></i>إضافة مبلغ جديد
                </a>
            </div>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="budget-container">
            <div class="text-center mb-4">
                <h2 class="text-primary"><i class="fas fa-chart-pie me-2"></i>إحصائيات البنود</h2>
            </div>

            <form method="GET" class="row g-3 mb-4">
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-calendar-alt me-2"></i>من تاريخ</label>
                    <input type="date" name="from" class="form-control" value="<?= htmlspecialchars($from) ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-calendar-alt me-2"></i>إلى تاريخ</label>
                    <input type="date" name="to" class="form-control" value="<?= htmlspecialchars($to) ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-success btn-hover w-100">
                        <i class="fas fa-chart-bar me-2"></i>عرض الإحصائيات
                    </button>
                </div>
            </form>

            <div class="stats-grid">
                <?php foreach ($categoryTotals as $row): ?>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="stat-number"><?= number_format($row['TotalAmount'], 2) ?></div>
                        <div class="stat-label"><?= htmlspecialchars($row['Category']) ?> (ج.م)</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div> <!-- إغلاق main-content -->

<!--<div class="row mt-4">
        <div class="col-md-4" class="cardCenter">
            <div class="card">
                <div class="card-header">
                    رصيد الموازنة
                </div>
                <div class="card-body">
                    <h5 class="card-title">مجموع الموازنة: <?php echo number_format($totalBudget, 2); ?> جنيه</h5>
                    <h5 class="card-title">إجمالي الصرف: <?php echo number_format($totalExpenses, 2); ?> جنيه</h5>
                    <h5 class="card-title">الرصيد المتبقي: <?php echo number_format($remainingBudget, 2); ?> جنيه</h5>
                </div>
            </div>
        </div>
    </div>-->

        <!--<div class="alert alert-info text-center">
            قم باستخدام الخيارات أعلاه للتنقل بين صفحات النظام.
        </div>
    </div>--><script src="script.js"></script>
</body>
</html>