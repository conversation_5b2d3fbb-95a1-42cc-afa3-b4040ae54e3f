# دليل التثبيت والإعداد - نظام إدارة المهام المحسن 🚀

## 📋 جدول المحتويات

1. [متطلبات النظام](#متطلبات-النظام)
2. [التحضير للتثبيت](#التحضير-للتثبيت)
3. [خطوات التثبيت](#خطوات-التثبيت)
4. [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
5. [إعداد خادم الويب](#إعداد-خادم-الويب)
6. [الاختبار والتحقق](#الاختبار-والتحقق)
7. [التكوين المتقدم](#التكوين-المتقدم)
8. [استكشاف الأخطاء](#استكشاف-الأخطاء)

## 🔧 متطلبات النظام

### الحد الأدنى للمتطلبات
```
✅ PHP 7.4 أو أحدث
✅ MySQL 5.7 أو أحدث / MariaDB 10.3+
✅ Apache 2.4+ أو Nginx 1.18+
✅ RAM: 512 MB
✅ مساحة التخزين: 100 MB
✅ دعم mod_rewrite (Apache)
```

### المتطلبات الموصى بها
```
🚀 PHP 8.0+ مع OPcache
🚀 MySQL 8.0+ أو MariaDB 10.5+
🚀 Apache 2.4+ مع mod_rewrite و mod_deflate
🚀 RAM: 2 GB أو أكثر
🚀 مساحة التخزين: 1 GB أو أكثر
🚀 SSL Certificate
🚀 Redis للتخزين المؤقت (اختياري)
```

### إضافات PHP المطلوبة
```php
- PDO و PDO_MySQL
- mbstring
- json
- openssl
- zip
- gd أو imagick
- curl
- fileinfo
```

## 🛠️ التحضير للتثبيت

### 1. تحقق من إصدار PHP
```bash
php -v
```

### 2. تحقق من إضافات PHP
```bash
php -m | grep -E "(pdo|mysql|mbstring|json|openssl|zip|gd|curl)"
```

### 3. تحقق من إصدار MySQL
```bash
mysql --version
```

### 4. إنشاء مجلد المشروع
```bash
sudo mkdir -p /var/www/html/task-management
sudo chown -R www-data:www-data /var/www/html/task-management
```

## 📥 خطوات التثبيت

### الطريقة الأولى: التحميل المباشر

#### 1. تحميل الملفات
```bash
cd /var/www/html/task-management
wget https://github.com/your-repo/task-management/archive/main.zip
unzip main.zip
mv task-management-main/* .
rm -rf task-management-main main.zip
```

#### 2. تعيين الصلاحيات
```bash
sudo chown -R www-data:www-data /var/www/html/task-management
sudo chmod -R 755 /var/www/html/task-management
sudo chmod -R 777 cache/ uploads/
```

### الطريقة الثانية: استخدام Git

#### 1. استنساخ المستودع
```bash
cd /var/www/html
git clone https://github.com/your-repo/task-management.git
cd task-management
```

#### 2. تعيين الصلاحيات
```bash
sudo chown -R www-data:www-data .
sudo chmod -R 755 .
sudo chmod -R 777 cache/ uploads/
```

## 🗄️ إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```sql
-- الدخول إلى MySQL
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE task_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم جديد (اختياري)
CREATE USER 'taskuser'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON task_management.* TO 'taskuser'@'localhost';
FLUSH PRIVILEGES;

-- الخروج
EXIT;
```

### 2. تشغيل ملف قاعدة البيانات
```bash
mysql -u taskuser -p task_management < update_tasks_database.sql
```

### 3. التحقق من إنشاء الجداول
```sql
mysql -u taskuser -p task_management

SHOW TABLES;
DESCRIBE notifications;
DESCRIBE task_categories;
DESCRIBE task_comments;
DESCRIBE task_history;
```

## ⚙️ إعداد الاتصال بقاعدة البيانات

### 1. إنشاء ملف الإعدادات
```bash
cp config.example.php config.php
```

### 2. تحديث بيانات الاتصال
```php
<?php
// config.php
return [
    'database' => [
        'host' => 'localhost',
        'dbname' => 'task_management',
        'username' => 'taskuser',
        'password' => 'strong_password_here',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],
    'app' => [
        'name' => 'نظام إدارة المهام',
        'version' => '4.0',
        'timezone' => 'Asia/Riyadh',
        'debug' => false
    ],
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'ttl' => 3600
    ]
];
?>
```

## 🌐 إعداد خادم الويب

### Apache Configuration

#### 1. إنشاء Virtual Host
```apache
# /etc/apache2/sites-available/task-management.conf
<VirtualHost *:80>
    ServerName task-management.local
    DocumentRoot /var/www/html/task-management
    
    <Directory /var/www/html/task-management>
        AllowOverride All
        Require all granted
        Options -Indexes
    </Directory>
    
    # تفعيل الضغط
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # تحسين الأداء
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    
    ErrorLog ${APACHE_LOG_DIR}/task-management_error.log
    CustomLog ${APACHE_LOG_DIR}/task-management_access.log combined
</VirtualHost>
```

#### 2. تفعيل الموقع
```bash
sudo a2ensite task-management.conf
sudo a2enmod rewrite deflate expires headers
sudo systemctl reload apache2
```

#### 3. إنشاء ملف .htaccess
```apache
# .htaccess
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (في الإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# حماية الملفات الحساسة
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعادة كتابة URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

### Nginx Configuration

#### 1. إعداد Server Block
```nginx
# /etc/nginx/sites-available/task-management
server {
    listen 80;
    server_name task-management.local;
    root /var/www/html/task-management;
    index index.php index.html;
    
    # تحسين الأداء
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # التخزين المؤقت
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # حماية الملفات الحساسة
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(sql|conf)$ {
        deny all;
    }
    
    # معالجة PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # إعادة كتابة URLs
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

#### 2. تفعيل الموقع
```bash
sudo ln -s /etc/nginx/sites-available/task-management /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## ✅ الاختبار والتحقق

### 1. اختبار الاتصال بقاعدة البيانات
```bash
cd /var/www/html/task-management
php -r "
require 'db.php';
echo 'Database connection: ';
try {
    \$pdo = new PDO(\$dsn, \$username, \$password, \$options);
    echo 'SUCCESS\n';
} catch (Exception \$e) {
    echo 'FAILED: ' . \$e->getMessage() . '\n';
}
"
```

### 2. اختبار صفحة الويب
```bash
curl -I http://task-management.local
```

### 3. اختبار الوظائف الأساسية
- ✅ تحميل الصفحة الرئيسية
- ✅ عرض قائمة المهام
- ✅ إضافة مهمة جديدة
- ✅ تحديث حالة المهمة
- ✅ البحث والفلترة
- ✅ تصدير التقارير

## 🔧 التكوين المتقدم

### 1. تفعيل HTTPS
```bash
# تثبيت Let's Encrypt
sudo apt install certbot python3-certbot-apache

# الحصول على شهادة SSL
sudo certbot --apache -d task-management.yourdomain.com
```

### 2. إعداد Redis للتخزين المؤقت
```bash
# تثبيت Redis
sudo apt install redis-server

# تفعيل Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

```php
// تحديث config.php
'cache' => [
    'enabled' => true,
    'driver' => 'redis',
    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => null,
        'database' => 0
    ]
]
```

### 3. إعداد Cron Jobs للمهام المجدولة
```bash
# تحرير crontab
crontab -e

# إضافة المهام المجدولة
# تنظيف التخزين المؤقت كل ساعة
0 * * * * /usr/bin/php /var/www/html/task-management/cron/cleanup_cache.php

# إرسال تذكيرات يومية
0 9 * * * /usr/bin/php /var/www/html/task-management/cron/daily_reminders.php

# تحديث الإحصائيات كل 15 دقيقة
*/15 * * * * /usr/bin/php /var/www/html/task-management/cron/update_stats.php
```

### 4. إعداد النسخ الاحتياطية
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/task-management"

# إنشاء مجلد النسخ الاحتياطية
mkdir -p $BACKUP_DIR

# نسخ احتياطية لقاعدة البيانات
mysqldump -u taskuser -p task_management > $BACKUP_DIR/database_$DATE.sql

# نسخ احتياطية للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/html/task-management

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "500 Internal Server Error"
```bash
# تحقق من سجلات الأخطاء
sudo tail -f /var/log/apache2/error.log
# أو
sudo tail -f /var/log/nginx/error.log

# تحقق من صلاحيات الملفات
sudo chown -R www-data:www-data /var/www/html/task-management
sudo chmod -R 755 /var/www/html/task-management
```

#### 2. خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من حالة MySQL
sudo systemctl status mysql

# تحقق من بيانات الاتصال
mysql -u taskuser -p task_management
```

#### 3. مشاكل في الأداء
```bash
# تفعيل OPcache
echo "opcache.enable=1" >> /etc/php/8.0/apache2/php.ini
echo "opcache.memory_consumption=128" >> /etc/php/8.0/apache2/php.ini
echo "opcache.max_accelerated_files=4000" >> /etc/php/8.0/apache2/php.ini

# إعادة تشغيل Apache
sudo systemctl restart apache2
```

#### 4. مشاكل في التصدير
```bash
# تحقق من مساحة القرص
df -h

# تحقق من صلاحيات مجلد التصدير
sudo chmod 777 /var/www/html/task-management/exports/
```

### أدوات التشخيص

#### 1. فحص النظام
```bash
# معلومات PHP
php -i | grep -E "(version|memory_limit|max_execution_time|upload_max_filesize)"

# معلومات MySQL
mysql -u taskuser -p -e "SHOW VARIABLES LIKE '%version%';"

# مساحة القرص
df -h /var/www/html/task-management
```

#### 2. مراقبة الأداء
```bash
# مراقبة استهلاك الذاكرة
top -p $(pgrep -f apache2)

# مراقبة الاتصالات
netstat -an | grep :80 | wc -l
```

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أثناء التثبيت:

1. **راجع سجلات الأخطاء** أولاً
2. **تحقق من المتطلبات** مرة أخرى
3. **ابحث في الوثائق** عن حلول مشابهة
4. **تواصل مع فريق الدعم** إذا لزم الأمر

---

**تم إعداد هذا الدليل بعناية لضمان تثبيت ناجح وآمن للنظام 🚀**
