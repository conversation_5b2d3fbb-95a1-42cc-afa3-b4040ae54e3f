-- ملف تحسين الأداء - إضافة فهارس لجدول المهام
-- يجب تشغيل هذا الملف لتحسين أداء صفحة إدارة المهام

-- التحقق من وجود الفهارس وإضافتها إذا لم تكن موجودة

-- فهر<PERSON> للبحث بالتاريخ (الأهم)
CREATE INDEX IF NOT EXISTS idx_notifications_dates ON Notifications(NotifyDate, ToDate);

-- فهرس للبحث بالحالة
CREATE INDEX IF NOT EXISTS idx_notifications_status ON Notifications(Status);

-- فهر<PERSON> مركب للبحث بالتاريخ والحالة معاً
CREATE INDEX IF NOT EXISTS idx_notifications_date_status ON Notifications(NotifyDate, Status);

-- فهرس للبحث في العنوان والرسالة (للبحث النصي)
CREATE FULLTEXT INDEX IF NOT EXISTS idx_notifications_search ON Notifications(Title, Message);

-- فهر<PERSON> للترتيب بالتاريخ
CREATE INDEX IF NOT EXISTS idx_notifications_order ON Notifications(NotifyDate DESC, ToDate DESC);

-- فهرس للحقول الجديدة إذا كانت موجودة
-- (سيتم تجاهلها إذا لم تكن الأعمدة موجودة)
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON Notifications(Priority);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON Notifications(Category);
CREATE INDEX IF NOT EXISTS idx_notifications_assigned ON Notifications(AssignedTo);
CREATE INDEX IF NOT EXISTS idx_notifications_created_by ON Notifications(CreatedBy);

-- فهرس مركب للاستعلامات المعقدة
CREATE INDEX IF NOT EXISTS idx_notifications_complex ON Notifications(Status, NotifyDate, ToDate);

-- تحسين جدول المستخدمين للربط
CREATE INDEX IF NOT EXISTS idx_users_username ON users(Username);

-- إحصائيات الجداول لتحسين الاستعلامات
ANALYZE TABLE Notifications;
ANALYZE TABLE users;

-- رسالة تأكيد
SELECT 'تم إنشاء الفهارس بنجاح - سيتحسن أداء صفحة إدارة المهام الآن' as message;
