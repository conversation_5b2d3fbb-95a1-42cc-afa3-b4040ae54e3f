-- ===== تحديث قاعدة البيانات لنظام إدارة المهام المحسن =====
-- الإصدار: 4.0
-- التاريخ: 2025-07-14
-- المطور: Augment Agent

-- بدء المعاملة
START TRANSACTION;

-- التحقق من وجود الجدول الأساسي
CREATE TABLE IF NOT EXISTS `notifications` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Title` varchar(255) NOT NULL,
    `Message` text,
    `NotifyDate` date NOT NULL,
    `ToDate` date DEFAULT NULL,
    `Status` enum('لم تنفذ','تحت التنفيذ','تم','ملغي') DEFAULT 'لم تنفذ',
    `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة الحقول الجديدة إذا لم تكن موجودة
-- حقل الأولوية
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'Priority') > 0,
    'SELECT "Column Priority already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `Priority` ENUM("منخفضة", "متوسطة", "عالية", "عاجلة") DEFAULT "متوسطة" AFTER `Status`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل التصنيف
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'Category') > 0,
    'SELECT "Column Category already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `Category` VARCHAR(100) DEFAULT NULL AFTER `Priority`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل المرفقات
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'Attachments') > 0,
    'SELECT "Column Attachments already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `Attachments` TEXT DEFAULT NULL AFTER `Category`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل المسؤول عن المهمة
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'AssignedTo') > 0,
    'SELECT "Column AssignedTo already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `AssignedTo` INT(11) DEFAULT NULL AFTER `Attachments`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل التقدم (نسبة مئوية)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'Progress') > 0,
    'SELECT "Column Progress already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `Progress` INT(3) DEFAULT 0 AFTER `AssignedTo`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل الملاحظات الإضافية
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'Notes') > 0,
    'SELECT "Column Notes already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `Notes` TEXT DEFAULT NULL AFTER `Progress`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل آخر تحديث
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'LastUpdated') > 0,
    'SELECT "Column LastUpdated already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `LastUpdated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `Notes`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حقل المستخدم الذي أنشأ المهمة
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND column_name = 'CreatedBy') > 0,
    'SELECT "Column CreatedBy already exists"',
    'ALTER TABLE `notifications` ADD COLUMN `CreatedBy` INT(11) DEFAULT NULL AFTER `LastUpdated`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة فهارس محسنة للأداء (مع التحقق من الوجود)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_priority') > 0,
    'SELECT "Index idx_priority already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_priority` (`Priority`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_category') > 0,
    'SELECT "Index idx_category already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_category` (`Category`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_assigned_to') > 0,
    'SELECT "Index idx_assigned_to already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_assigned_to` (`AssignedTo`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_created_by') > 0,
    'SELECT "Index idx_created_by already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_created_by` (`CreatedBy`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_status_priority') > 0,
    'SELECT "Index idx_status_priority already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_status_priority` (`Status`, `Priority`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_notify_date') > 0,
    'SELECT "Index idx_notify_date already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_notify_date` (`NotifyDate`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_to_date') > 0,
    'SELECT "Index idx_to_date already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_to_date` (`ToDate`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND index_name = 'idx_title') > 0,
    'SELECT "Index idx_title already exists"',
    'ALTER TABLE `notifications` ADD INDEX `idx_title` (`Title`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إنشاء جدول التصنيفات المحسن
CREATE TABLE IF NOT EXISTS `task_categories` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Name` varchar(100) NOT NULL UNIQUE,
    `Description` text,
    `Color` varchar(7) DEFAULT '#007bff',
    `Icon` varchar(50) DEFAULT 'fas fa-tasks',
    `IsActive` tinyint(1) DEFAULT 1,
    `SortOrder` int(11) DEFAULT 0,
    `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
    `UpdatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `uk_name` (`Name`),
    KEY `idx_active` (`IsActive`),
    KEY `idx_sort_order` (`SortOrder`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التعليقات المحسن
CREATE TABLE IF NOT EXISTS `task_comments` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `TaskID` int(11) NOT NULL,
    `UserID` int(11) NOT NULL,
    `Comment` text NOT NULL,
    `IsEdited` tinyint(1) DEFAULT 0,
    `EditedAt` timestamp NULL DEFAULT NULL,
    `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    KEY `idx_task_id` (`TaskID`),
    KEY `idx_user_id` (`UserID`),
    KEY `idx_created_at` (`CreatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل التغييرات المحسن
CREATE TABLE IF NOT EXISTS `task_history` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `TaskID` int(11) NOT NULL,
    `UserID` int(11) NOT NULL,
    `Action` varchar(100) NOT NULL,
    `FieldName` varchar(50) DEFAULT NULL,
    `OldValue` text,
    `NewValue` text,
    `IPAddress` varchar(45) DEFAULT NULL,
    `UserAgent` text,
    `CreatedAt` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    KEY `idx_task_id` (`TaskID`),
    KEY `idx_user_id` (`UserID`),
    KEY `idx_action` (`Action`),
    KEY `idx_created_at` (`CreatedAt`),
    KEY `idx_field_name` (`FieldName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج التصنيفات الافتراضية
INSERT INTO `task_categories` (`Name`, `Description`, `Color`, `Icon`, `SortOrder`) VALUES
('عام', 'مهام عامة ومتنوعة', '#6c757d', 'fas fa-tasks', 1),
('مالية', 'مهام متعلقة بالشؤون المالية والمحاسبة', '#28a745', 'fas fa-dollar-sign', 2),
('إدارية', 'مهام إدارية وتنظيمية', '#007bff', 'fas fa-briefcase', 3),
('تقنية', 'مهام تقنية وتطوير البرمجيات', '#17a2b8', 'fas fa-code', 4),
('عاجل', 'مهام عاجلة تحتاج متابعة فورية', '#dc3545', 'fas fa-exclamation-triangle', 5),
('اجتماعات', 'مهام متعلقة بالاجتماعات والمؤتمرات', '#ffc107', 'fas fa-users', 6),
('تقارير', 'إعداد وتجهيز التقارير والإحصائيات', '#6f42c1', 'fas fa-chart-bar', 7),
('متابعة', 'مهام المتابعة والمراجعة', '#fd7e14', 'fas fa-eye', 8),
('تدريب', 'مهام التدريب وتطوير المهارات', '#20c997', 'fas fa-graduation-cap', 9),
('صيانة', 'مهام الصيانة والدعم التقني', '#e83e8c', 'fas fa-tools', 10)
ON DUPLICATE KEY UPDATE
    `Description` = VALUES(`Description`),
    `Color` = VALUES(`Color`),
    `Icon` = VALUES(`Icon`),
    `SortOrder` = VALUES(`SortOrder`);

-- إضافة قيود المفاتيح الخارجية (مع التحقق من الوجود)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_assigned_to') > 0,
    'SELECT "Foreign key fk_assigned_to already exists"',
    'ALTER TABLE `notifications` ADD CONSTRAINT `fk_assigned_to` FOREIGN KEY (`AssignedTo`) REFERENCES `users` (`ID`) ON DELETE SET NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'notifications'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_created_by') > 0,
    'SELECT "Foreign key fk_created_by already exists"',
    'ALTER TABLE `notifications` ADD CONSTRAINT `fk_created_by` FOREIGN KEY (`CreatedBy`) REFERENCES `users` (`ID`) ON DELETE SET NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'task_comments'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_comment_task') > 0,
    'SELECT "Foreign key fk_comment_task already exists"',
    'ALTER TABLE `task_comments` ADD CONSTRAINT `fk_comment_task` FOREIGN KEY (`TaskID`) REFERENCES `notifications` (`ID`) ON DELETE CASCADE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'task_comments'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_comment_user') > 0,
    'SELECT "Foreign key fk_comment_user already exists"',
    'ALTER TABLE `task_comments` ADD CONSTRAINT `fk_comment_user` FOREIGN KEY (`UserID`) REFERENCES `users` (`ID`) ON DELETE CASCADE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'task_history'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_history_task') > 0,
    'SELECT "Foreign key fk_history_task already exists"',
    'ALTER TABLE `task_history` ADD CONSTRAINT `fk_history_task` FOREIGN KEY (`TaskID`) REFERENCES `notifications` (`ID`) ON DELETE CASCADE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE table_name = 'task_history'
     AND table_schema = DATABASE()
     AND constraint_name = 'fk_history_user') > 0,
    'SELECT "Foreign key fk_history_user already exists"',
    'ALTER TABLE `task_history` ADD CONSTRAINT `fk_history_user` FOREIGN KEY (`UserID`) REFERENCES `users` (`ID`) ON DELETE CASCADE'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- تحديث المهام الموجودة لتعيين المنشئ الافتراضي
UPDATE `notifications` SET `CreatedBy` = 1 WHERE `CreatedBy` IS NULL;

-- إنهاء المعاملة
COMMIT;

-- رسالة نجاح
SELECT 'تم تحديث قاعدة البيانات بنجاح - نظام إدارة المهام المحسن جاهز للاستخدام!' as 'Status';
