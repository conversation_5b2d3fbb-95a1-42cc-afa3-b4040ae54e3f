<?php
/**
 * جلب الإحصائيات التفاعلية المحسنة للمهام
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json; charset=utf-8');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

try {
    // الحصول على نوع الإحصائيات المطلوبة
    $type = $_GET['type'] ?? 'all';
    $period = $_GET['period'] ?? 'all'; // today, week, month, year, all
    
    $stats = [];
    
    switch ($type) {
        case 'basic':
            $stats = getBasicStats($conn, $period);
            break;
        case 'categories':
            $stats = getCategoryStats($conn, $period);
            break;
        case 'users':
            $stats = getUserStats($conn, $period);
            break;
        case 'performance':
            $stats = getPerformanceStats($conn, $period);
            break;
        case 'time':
            $stats = getTimeStats($conn);
            break;
        case 'priority':
            $stats = getPriorityStats($conn, $period);
            break;
        case 'trends':
            $stats = getTrendStats($conn);
            break;
        default:
            $stats = [
                'basic' => getBasicStats($conn, $period),
                'categories' => getCategoryStats($conn, $period),
                'users' => getUserStats($conn, $period),
                'performance' => getPerformanceStats($conn, $period),
                'time' => getTimeStats($conn),
                'priority' => getPriorityStats($conn, $period),
                'trends' => getTrendStats($conn)
            ];
    }
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'period' => $period,
        'generated_at' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب الإحصائيات',
        'error' => $e->getMessage()
    ]);
}

/**
 * الإحصائيات الأساسية
 */
function getBasicStats($conn, $period = 'all') {
    $whereClause = getPeriodWhereClause($period);
    
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN Status = 'ملغي' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            SUM(CASE WHEN NotifyDate = CURDATE() THEN 1 ELSE 0 END) as today,
            SUM(CASE WHEN NotifyDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as this_week,
            AVG(Progress) as avg_progress,
            MIN(CreatedAt) as first_task_date,
            MAX(LastUpdated) as last_update_date
        FROM notifications
        $whereClause
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // حساب معدلات الإنجاز
    if ($result['total'] > 0) {
        $result['completion_rate'] = round(($result['completed'] / $result['total']) * 100, 1);
        $result['overdue_rate'] = round(($result['overdue'] / $result['total']) * 100, 1);
        $result['in_progress_rate'] = round(($result['in_progress'] / $result['total']) * 100, 1);
    } else {
        $result['completion_rate'] = 0;
        $result['overdue_rate'] = 0;
        $result['in_progress_rate'] = 0;
    }
    
    $result['avg_progress'] = round($result['avg_progress'], 1);
    
    // تنسيق التواريخ
    $result['first_task_date_formatted'] = $result['first_task_date'] ? 
        date('d/m/Y', strtotime($result['first_task_date'])) : '';
    $result['last_update_date_formatted'] = $result['last_update_date'] ? 
        date('d/m/Y H:i', strtotime($result['last_update_date'])) : '';
    
    return $result;
}

/**
 * إحصائيات التصنيفات
 */
function getCategoryStats($conn, $period = 'all') {
    $whereClause = getPeriodWhereClause($period);
    
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(n.Category, 'غير محدد') as category,
            tc.Color as color,
            tc.Icon as icon,
            COUNT(*) as count,
            SUM(CASE WHEN n.Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN n.Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN n.Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            ROUND(AVG(n.Progress), 1) as avg_progress,
            ROUND((SUM(CASE WHEN n.Status = 'تم' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as completion_rate
        FROM notifications n
        LEFT JOIN task_categories tc ON n.Category = tc.Name
        $whereClause
        GROUP BY n.Category, tc.Color, tc.Icon
        ORDER BY count DESC
    ");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إضافة ألوان افتراضية للتصنيفات غير المحددة
    foreach ($results as &$result) {
        if (!$result['color']) {
            $result['color'] = '#6c757d';
        }
        if (!$result['icon']) {
            $result['icon'] = 'fas fa-folder';
        }
    }
    
    return $results;
}

/**
 * إحصائيات المستخدمين
 */
function getUserStats($conn, $period = 'all') {
    $whereClause = getPeriodWhereClause($period, 'n');
    
    $stmt = $conn->prepare("
        SELECT 
            u.ID as user_id,
            u.Username,
            u.FullName,
            COUNT(n.ID) as assigned_tasks,
            SUM(CASE WHEN n.Status = 'تم' THEN 1 ELSE 0 END) as completed_tasks,
            SUM(CASE WHEN n.Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress_tasks,
            SUM(CASE WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 1 ELSE 0 END) as overdue_tasks,
            ROUND(AVG(n.Progress), 1) as avg_progress,
            ROUND((SUM(CASE WHEN n.Status = 'تم' THEN 1 ELSE 0 END) / COUNT(n.ID)) * 100, 1) as completion_rate
        FROM users u
        LEFT JOIN notifications n ON u.ID = n.AssignedTo
        WHERE n.ID IS NOT NULL $whereClause
        GROUP BY u.ID, u.Username, u.FullName
        HAVING assigned_tasks > 0
        ORDER BY completion_rate DESC, assigned_tasks DESC
        LIMIT 15
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * إحصائيات الأداء
 */
function getPerformanceStats($conn, $period = 'all') {
    $whereClause = getPeriodWhereClause($period);
    
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_tasks,
            AVG(CASE 
                WHEN Status = 'تم' AND LastUpdated IS NOT NULL THEN 
                    DATEDIFF(LastUpdated, CreatedAt)
                ELSE NULL 
            END) as avg_completion_days,
            AVG(CASE 
                WHEN Status = 'تم' AND LastUpdated IS NOT NULL THEN 
                    DATEDIFF(LastUpdated, NotifyDate)
                ELSE NULL 
            END) as avg_response_days,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue_count,
            SUM(CASE WHEN Status = 'تم' AND LastUpdated <= ToDate THEN 1 ELSE 0 END) as on_time_completed,
            COUNT(CASE WHEN Status = 'تم' THEN 1 END) as total_completed
        FROM notifications
        $whereClause
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // حساب المعدلات
    if ($result['total_tasks'] > 0) {
        $result['overdue_rate'] = round(($result['overdue_count'] / $result['total_tasks']) * 100, 1);
    } else {
        $result['overdue_rate'] = 0;
    }
    
    if ($result['total_completed'] > 0) {
        $result['on_time_rate'] = round(($result['on_time_completed'] / $result['total_completed']) * 100, 1);
    } else {
        $result['on_time_rate'] = 0;
    }
    
    $result['avg_completion_days'] = round($result['avg_completion_days'], 1);
    $result['avg_response_days'] = round($result['avg_response_days'], 1);
    
    return $result;
}

/**
 * إحصائيات الأولوية
 */
function getPriorityStats($conn, $period = 'all') {
    $whereClause = getPeriodWhereClause($period);
    
    $stmt = $conn->prepare("
        SELECT 
            Priority,
            COUNT(*) as count,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            ROUND(AVG(Progress), 1) as avg_progress,
            ROUND((SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as completion_rate
        FROM notifications
        $whereClause
        GROUP BY Priority
        ORDER BY 
            CASE Priority 
                WHEN 'عاجلة' THEN 1 
                WHEN 'عالية' THEN 2 
                WHEN 'متوسطة' THEN 3 
                WHEN 'منخفضة' THEN 4 
                ELSE 5 
            END
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * إحصائيات زمنية
 */
function getTimeStats($conn) {
    $stmt = $conn->prepare("
        SELECT
            'اليوم' as period,
            'today' as period_key,
            COUNT(*) as count,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress
        FROM notifications
        WHERE DATE(CreatedAt) = CURDATE()
        
        UNION ALL
        
        SELECT
            'أمس' as period,
            'yesterday' as period_key,
            COUNT(*) as count,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress
        FROM notifications
        WHERE DATE(CreatedAt) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        
        UNION ALL
        
        SELECT
            'هذا الأسبوع' as period,
            'this_week' as period_key,
            COUNT(*) as count,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress
        FROM notifications
        WHERE YEARWEEK(CreatedAt, 1) = YEARWEEK(CURDATE(), 1)
        
        UNION ALL
        
        SELECT
            'هذا الشهر' as period,
            'this_month' as period_key,
            COUNT(*) as count,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress
        FROM notifications
        WHERE YEAR(CreatedAt) = YEAR(CURDATE()) AND MONTH(CreatedAt) = MONTH(CURDATE())
    ");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب معدل الإنجاز لكل فترة
    foreach ($results as &$result) {
        if ($result['count'] > 0) {
            $result['completion_rate'] = round(($result['completed'] / $result['count']) * 100, 1);
        } else {
            $result['completion_rate'] = 0;
        }
    }
    
    return $results;
}

/**
 * إحصائيات الاتجاهات
 */
function getTrendStats($conn) {
    $stmt = $conn->prepare("
        SELECT 
            DATE(CreatedAt) as date,
            COUNT(*) as created,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed
        FROM notifications
        WHERE CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(CreatedAt)
        ORDER BY date DESC
        LIMIT 30
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على شرط الفترة الزمنية
 */
function getPeriodWhereClause($period, $tableAlias = '') {
    $prefix = $tableAlias ? $tableAlias . '.' : '';
    
    switch ($period) {
        case 'today':
            return "WHERE DATE({$prefix}CreatedAt) = CURDATE()";
        case 'week':
            return "WHERE YEARWEEK({$prefix}CreatedAt, 1) = YEARWEEK(CURDATE(), 1)";
        case 'month':
            return "WHERE YEAR({$prefix}CreatedAt) = YEAR(CURDATE()) AND MONTH({$prefix}CreatedAt) = MONTH(CURDATE())";
        case 'year':
            return "WHERE YEAR({$prefix}CreatedAt) = YEAR(CURDATE())";
        default:
            return '';
    }
}
?>
