<?php
/**
 * نظام التصدير والطباعة المحسن للمهام
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// الحصول على نوع التصدير
$exportType = $_GET['type'] ?? 'excel';
$allowedTypes = ['excel', 'csv', 'pdf', 'json'];

if (!in_array($exportType, $allowedTypes)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'نوع التصدير غير مدعوم']);
    exit;
}

try {
    // الحصول على فلاتر البحث
    $filters = getExportFilters();

    // جلب البيانات
    $tasks = getTasksForExport($conn, $filters);
    $stats = getExportStats($conn, $filters);

    // تسجيل عملية التصدير
    logExportActivity($_SESSION['user_id'], $exportType, count($tasks));

    // تصدير البيانات حسب النوع المطلوب
    switch ($exportType) {
        case 'excel':
            exportToExcel($tasks, $stats, $filters);
            break;
        case 'csv':
            exportToCSV($tasks, $filters);
            break;
        case 'pdf':
            exportToPDF($tasks, $stats, $filters);
            break;
        case 'json':
            exportToJSON($tasks, $stats, $filters);
            break;
    }

} catch (Exception $e) {
    error_log("خطأ في التصدير: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في التصدير']);
}

/**
 * الحصول على فلاتر التصدير
 */
function getExportFilters() {
    return [
        'search' => trim($_GET['search'] ?? ''),
        'status' => $_GET['status'] ?? '',
        'priority' => $_GET['priority'] ?? '',
        'category' => $_GET['category'] ?? '',
        'assigned_to' => $_GET['assigned_to'] ?? '',
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'created_from' => $_GET['created_from'] ?? '',
        'created_to' => $_GET['created_to'] ?? '',
        'include_completed' => $_GET['include_completed'] ?? '1',
        'include_notes' => $_GET['include_notes'] ?? '1',
        'include_comments' => $_GET['include_comments'] ?? '0'
    ];
}

/**
 * جلب المهام للتصدير
 */
function getTasksForExport($conn, $filters) {
    $sql = "SELECT
        n.ID, n.Title, n.Message, n.NotifyDate, n.ToDate, n.Status,
        n.Priority, n.Category, n.Progress, n.Notes, n.CreatedAt, n.LastUpdated,
        u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
        u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
        tc.Color as CategoryColor, tc.Icon as CategoryIcon,

        -- حسابات إضافية
        CASE
            WHEN n.Status = 'تم' THEN 'مكتمل'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN CONCAT('متأخر ', DATEDIFF(CURDATE(), n.ToDate), ' يوم')
            WHEN n.ToDate = CURDATE() THEN 'ينتهي اليوم'
            WHEN n.ToDate > CURDATE() THEN CONCAT(DATEDIFF(n.ToDate, CURDATE()), ' يوم متبقي')
            ELSE ''
        END as days_remaining,

        DATEDIFF(COALESCE(n.LastUpdated, CURDATE()), n.CreatedAt) as duration_days,

        (SELECT COUNT(*) FROM task_comments tc WHERE tc.TaskID = n.ID) as comments_count";

    // إضافة التعليقات إذا كانت مطلوبة
    if ($filters['include_comments'] === '1') {
        $sql .= ",
        (SELECT GROUP_CONCAT(
            CONCAT(u.FullName, ': ', tc.Comment, ' (', DATE_FORMAT(tc.CreatedAt, '%d/%m/%Y'), ')')
            SEPARATOR '\n'
        ) FROM task_comments tc
        LEFT JOIN users u ON tc.UserID = u.ID
        WHERE tc.TaskID = n.ID
        ORDER BY tc.CreatedAt ASC) as all_comments";
    }

    $sql .= " FROM notifications n
    LEFT JOIN users u1 ON n.CreatedBy = u1.ID
    LEFT JOIN users u2 ON n.AssignedTo = u2.ID
    LEFT JOIN task_categories tc ON n.Category = tc.Name";

    $conditions = [];
    $params = [];

    // تطبيق الفلاتر
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ? OR n.Notes LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['status'])) {
        $conditions[] = "n.Status = ?";
        $params[] = $filters['status'];
    }

    if ($filters['include_completed'] === '0') {
        $conditions[] = "n.Status != 'تم'";
    }

    if (!empty($filters['priority'])) {
        $conditions[] = "n.Priority = ?";
        $params[] = $filters['priority'];
    }

    if (!empty($filters['category'])) {
        $conditions[] = "n.Category = ?";
        $params[] = $filters['category'];
    }

    if (!empty($filters['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $params[] = $filters['assigned_to'];
    }

    if (!empty($filters['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $params[] = $filters['date_from'];
    }

    if (!empty($filters['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $params[] = $filters['date_to'];
    }

    if (!empty($filters['created_from'])) {
        $conditions[] = "DATE(n.CreatedAt) >= ?";
        $params[] = $filters['created_from'];
    }

    if (!empty($filters['created_to'])) {
        $conditions[] = "DATE(n.CreatedAt) <= ?";
        $params[] = $filters['created_to'];
    }

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY
        CASE n.Priority
            WHEN 'عاجلة' THEN 1
            WHEN 'عالية' THEN 2
            WHEN 'متوسطة' THEN 3
            WHEN 'منخفضة' THEN 4
        END,
        n.NotifyDate ASC";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * جلب إحصائيات التصدير
 */
function getExportStats($conn, $filters) {
    $whereClause = '';
    $params = [];
    $conditions = [];

    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(Title LIKE ? OR Message LIKE ? OR Notes LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['status'])) {
        $conditions[] = "Status = ?";
        $params[] = $filters['status'];
    }

    if ($filters['include_completed'] === '0') {
        $conditions[] = "Status != 'تم'";
    }

    if (!empty($filters['priority'])) {
        $conditions[] = "Priority = ?";
        $params[] = $filters['priority'];
    }

    if (!empty($filters['category'])) {
        $conditions[] = "Category = ?";
        $params[] = $filters['category'];
    }

    if (!empty($conditions)) {
        $whereClause = " WHERE " . implode(" AND ", $conditions);
    }

    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN Status = 'ملغي' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            SUM(CASE WHEN NotifyDate = CURDATE() THEN 1 ELSE 0 END) as today,
            AVG(Progress) as avg_progress,
            AVG(CASE WHEN Status = 'تم' THEN DATEDIFF(LastUpdated, CreatedAt) END) as avg_completion_days,

            -- إحصائيات الأولوية
            SUM(CASE WHEN Priority = 'عاجلة' THEN 1 ELSE 0 END) as urgent_count,
            SUM(CASE WHEN Priority = 'عالية' THEN 1 ELSE 0 END) as high_count,
            SUM(CASE WHEN Priority = 'متوسطة' THEN 1 ELSE 0 END) as medium_count,
            SUM(CASE WHEN Priority = 'منخفضة' THEN 1 ELSE 0 END) as low_count

        FROM notifications
        $whereClause
    ");

    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    // حساب المعدلات
    if ($result['total'] > 0) {
        $result['completion_rate'] = round(($result['completed'] / $result['total']) * 100, 1);
        $result['overdue_rate'] = round(($result['overdue'] / $result['total']) * 100, 1);
        $result['in_progress_rate'] = round(($result['in_progress'] / $result['total']) * 100, 1);
    } else {
        $result['completion_rate'] = 0;
        $result['overdue_rate'] = 0;
        $result['in_progress_rate'] = 0;
    }

    $result['avg_progress'] = round($result['avg_progress'], 1);
    $result['avg_completion_days'] = round($result['avg_completion_days'], 1);

    return $result;
}

/**
 * تصدير إلى Excel
 */
function exportToExcel($tasks, $stats, $filters) {
    $html = generateAdvancedExcelHTML($tasks, $stats, $filters);

    $filename = 'tasks_report_' . date('Y-m-d_H-i-s') . '.xls';
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    echo $html;
}

/**
 * تصدير إلى CSV محسن
/**
 * إنشاء HTML متقدم للExcel
 */
function generateAdvancedExcelHTML($tasks, $stats, $filters) {
    $html = '<!DOCTYPE html>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تقرير المهام المفصل</title>
        <style>
            body {
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                margin: 20px;
                background: #f8f9fa;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
                font-size: 12px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: right;
                vertical-align: top;
            }
            th {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                font-weight: bold;
                text-align: center;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 3px solid #007bff;
                padding-bottom: 20px;
            }
            .header h1 {
                color: #007bff;
                margin: 0;
                font-size: 28px;
            }
            .header p {
                color: #6c757d;
                margin: 5px 0;
            }
            .stats {
                margin-bottom: 30px;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            .stat-card {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                border-right: 4px solid #007bff;
                text-align: center;
            }
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 5px;
            }
            .stat-label {
                color: #6c757d;
                font-size: 14px;
            }
            .filters-applied {
                background: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .status-completed { background-color: #d4edda; color: #155724; }
            .status-in-progress { background-color: #fff3cd; color: #856404; }
            .status-pending { background-color: #f8d7da; color: #721c24; }
            .priority-urgent { background-color: #f8d7da; color: #721c24; font-weight: bold; }
            .priority-high { background-color: #fff3cd; color: #856404; }
            .priority-medium { background-color: #d1ecf1; color: #0c5460; }
            .priority-low { background-color: #d4edda; color: #155724; }
            .progress-bar {
                background: #e9ecef;
                height: 20px;
                border-radius: 10px;
                overflow: hidden;
                position: relative;
            }
            .progress-fill {
                background: linear-gradient(90deg, #28a745, #20c997);
                height: 100%;
                border-radius: 10px;
                text-align: center;
                line-height: 20px;
                color: white;
                font-size: 11px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
    <div class="container">';

    // رأس التقرير
    $html .= '<div class="header">';
    $html .= '<h1>📊 تقرير المهام المفصل</h1>';
    $html .= '<p><strong>تاريخ التقرير:</strong> ' . date('d/m/Y H:i') . '</p>';
    $html .= '<p><strong>المستخدم:</strong> ' . ($_SESSION['username'] ?? 'غير محدد') . '</p>';
    $html .= '<p><strong>عدد المهام:</strong> ' . count($tasks) . ' مهمة</p>';
    $html .= '</div>';

    // الفلاتر المطبقة
    $appliedFilters = array_filter($filters);
    if (!empty($appliedFilters)) {
        $html .= '<div class="filters-applied">';
        $html .= '<h3>🔍 الفلاتر المطبقة:</h3>';
        $html .= '<ul>';
        foreach ($appliedFilters as $key => $value) {
            if ($value && $value !== '1') {
                $filterName = getFilterDisplayName($key);
                $html .= '<li><strong>' . $filterName . ':</strong> ' . htmlspecialchars($value) . '</li>';
            }
        }
        $html .= '</ul>';
        $html .= '</div>';
    }

    // الإحصائيات المتقدمة
    $html .= '<div class="stats">';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['total'] . '</div><div class="stat-label">إجمالي المهام</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['completed'] . '</div><div class="stat-label">مكتملة</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['in_progress'] . '</div><div class="stat-label">قيد التنفيذ</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['pending'] . '</div><div class="stat-label">في الانتظار</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['overdue'] . '</div><div class="stat-label">متأخرة</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['completion_rate'] . '%</div><div class="stat-label">معدل الإنجاز</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['avg_progress'] . '%</div><div class="stat-label">متوسط التقدم</div></div>';
    $html .= '<div class="stat-card"><div class="stat-number">' . $stats['avg_completion_days'] . '</div><div class="stat-label">متوسط أيام الإنجاز</div></div>';
    $html .= '</div>';

    // جدول المهام المفصل
    $html .= '<h2>📋 تفاصيل المهام</h2>';
    $html .= '<table>';
    $html .= '<thead>';
    $html .= '<tr>';
    $html .= '<th style="width: 50px;">الرقم</th>';
    $html .= '<th style="width: 200px;">العنوان</th>';
    $html .= '<th style="width: 250px;">الوصف</th>';
    $html .= '<th style="width: 100px;">تاريخ البداية</th>';
    $html .= '<th style="width: 100px;">تاريخ الانتهاء</th>';
    $html .= '<th style="width: 80px;">الحالة</th>';
    $html .= '<th style="width: 80px;">الأولوية</th>';
    $html .= '<th style="width: 100px;">التصنيف</th>';
    $html .= '<th style="width: 120px;">التقدم</th>';
    $html .= '<th style="width: 120px;">المسؤول</th>';
    $html .= '<th style="width: 100px;">الأيام المتبقية</th>';
    $html .= '<th style="width: 80px;">التعليقات</th>';
    if ($filters['include_notes'] === '1') {
        $html .= '<th style="width: 200px;">الملاحظات</th>';
    }
    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody>';

    foreach ($tasks as $index => $task) {
        $rowClass = '';
        if ($task['Status'] === 'تم') $rowClass = 'status-completed';
        elseif ($task['Status'] === 'تحت التنفيذ') $rowClass = 'status-in-progress';
        elseif (strpos($task['days_remaining'], 'متأخر') !== false) $rowClass = 'status-pending';

        $html .= '<tr class="' . $rowClass . '">';
        $html .= '<td style="text-align: center; font-weight: bold;">' . $task['ID'] . '</td>';
        $html .= '<td><strong>' . htmlspecialchars($task['Title']) . '</strong></td>';
        $html .= '<td>' . htmlspecialchars(truncateText(strip_tags($task['Message']), 100)) . '</td>';
        $html .= '<td style="text-align: center;">' . formatDate($task['NotifyDate']) . '</td>';
        $html .= '<td style="text-align: center;">' . formatDate($task['ToDate']) . '</td>';

        // الحالة مع تنسيق
        $statusClass = 'status-' . strtolower(str_replace(' ', '-', $task['Status']));
        $html .= '<td class="' . $statusClass . '" style="text-align: center; font-weight: bold;">' . $task['Status'] . '</td>';

        // الأولوية مع تنسيق
        $priorityClass = 'priority-' . getPriorityEnglish($task['Priority']);
        $html .= '<td class="' . $priorityClass . '" style="text-align: center; font-weight: bold;">' . $task['Priority'] . '</td>';

        $html .= '<td style="text-align: center;">' . ($task['Category'] ?: 'غير محدد') . '</td>';

        // شريط التقدم
        $html .= '<td>';
        $html .= '<div class="progress-bar">';
        $html .= '<div class="progress-fill" style="width: ' . $task['Progress'] . '%;">' . $task['Progress'] . '%</div>';
        $html .= '</div>';
        $html .= '</td>';

        $html .= '<td>' . ($task['AssignedToFullName'] ?: $task['AssignedToName'] ?: 'غير محدد') . '</td>';
        $html .= '<td style="text-align: center;">' . $task['days_remaining'] . '</td>';
        $html .= '<td style="text-align: center;">' . ($task['comments_count'] > 0 ? $task['comments_count'] . ' تعليق' : '-') . '</td>';

        if ($filters['include_notes'] === '1') {
            $html .= '<td>' . htmlspecialchars(truncateText(strip_tags($task['Notes']), 100)) . '</td>';
        }

        $html .= '</tr>';
    }

    $html .= '</tbody>';
    $html .= '</table>';

    // تذييل التقرير
    $html .= '<div style="margin-top: 30px; padding-top: 20px; border-top: 2px solid #007bff; text-align: center; color: #6c757d;">';
    $html .= '<p>تم إنشاء هذا التقرير بواسطة نظام إدارة المهام المحسن</p>';
    $html .= '<p>© ' . date('Y') . ' - جميع الحقوق محفوظة</p>';
    $html .= '</div>';

    $html .= '</div></body></html>';

    return $html;
}

/**
 * إنشاء HTML متقدم للPDF
 */
function generateAdvancedPDFHTML($tasks, $stats, $filters) {
    // نفس المحتوى مع تعديلات للPDF
    return generateAdvancedExcelHTML($tasks, $stats, $filters);
}

/**
 * الحصول على اسم الفلتر للعرض
 */
function getFilterDisplayName($filterKey) {
    $names = [
        'search' => 'البحث',
        'status' => 'الحالة',
        'priority' => 'الأولوية',
        'category' => 'التصنيف',
        'assigned_to' => 'المسؤول',
        'date_from' => 'من تاريخ',
        'date_to' => 'إلى تاريخ',
        'created_from' => 'تاريخ الإنشاء من',
        'created_to' => 'تاريخ الإنشاء إلى'
    ];

    return $names[$filterKey] ?? $filterKey;
}

/**
 * تحويل الأولوية للإنجليزية للCSS
 */
function getPriorityEnglish($priority) {
    $priorities = [
        'عاجلة' => 'urgent',
        'عالية' => 'high',
        'متوسطة' => 'medium',
        'منخفضة' => 'low'
    ];

    return $priorities[$priority] ?? 'medium';
}

/**
 * تقصير النص
 */
function truncateText($text, $length = 50) {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length) . '...';
}

/**
 * إنشاء PDF باستخدام TCPDF
 */
function generateTCPDF($html, $filename) {
    // يتطلب مكتبة TCPDF
    // هذا مثال أساسي - يمكن تطويره أكثر

    require_once('tcpdf/tcpdf.php');

    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    $pdf->SetCreator('نظام إدارة المهام');
    $pdf->SetAuthor('نظام إدارة المهام');
    $pdf->SetTitle('تقرير المهام');
    $pdf->SetSubject('تقرير مفصل للمهام');

    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

    $pdf->AddPage();
    $pdf->writeHTML($html, true, false, true, false, '');

    $pdf->Output($filename, 'D');
}

/**
 * إنشاء PDF باستخدام DomPDF
 */
function generateDomPDF($html, $filename) {
    // يتطلب مكتبة DomPDF

    require_once 'dompdf/autoload.inc.php';

    use Dompdf\Dompdf;
    use Dompdf\Options;

    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'landscape');
    $dompdf->render();

    $dompdf->stream($filename, array("Attachment" => true));
}
?>

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";

    $output = fopen('php://output', 'w');

    // كتابة رأس الجدول
    $headers = [
        'الرقم', 'العنوان', 'الوصف', 'تاريخ البداية', 'تاريخ الانتهاء',
        'الحالة', 'الأولوية', 'التصنيف', 'التقدم (%)', 'المسؤول',
        'المنشئ', 'تاريخ الإنشاء', 'آخر تحديث', 'الأيام المتبقية',
        'مدة المهمة (أيام)', 'عدد التعليقات'
    ];

    if ($filters['include_notes'] === '1') {
        $headers[] = 'الملاحظات';
    }

    if ($filters['include_comments'] === '1') {
        $headers[] = 'التعليقات';
    }

    fputcsv($output, $headers);

    // كتابة البيانات
    foreach ($tasks as $task) {
        $row = [
            $task['ID'],
            $task['Title'],
            strip_tags($task['Message']),
            formatDate($task['NotifyDate']),
            formatDate($task['ToDate']),
            $task['Status'],
            $task['Priority'],
            $task['Category'] ?: 'غير محدد',
            $task['Progress'],
            $task['AssignedToFullName'] ?: $task['AssignedToName'] ?: 'غير محدد',
            $task['CreatedByFullName'] ?: $task['CreatedByName'] ?: 'غير محدد',
            formatDateTime($task['CreatedAt']),
            formatDateTime($task['LastUpdated']),
            $task['days_remaining'],
            $task['duration_days'],
            $task['comments_count']
        ];

        if ($filters['include_notes'] === '1') {
            $row[] = strip_tags($task['Notes']);
        }

        if ($filters['include_comments'] === '1') {
            $row[] = $task['all_comments'] ?? '';
        }

        fputcsv($output, $row);
    }

    fclose($output);
}

/**
 * تصدير إلى JSON محسن
 */
function exportToJSON($tasks, $stats, $filters) {
    $data = [
        'export_info' => [
            'generated_at' => date('Y-m-d H:i:s'),
            'generated_by' => $_SESSION['username'] ?? 'Unknown',
            'user_id' => $_SESSION['user_id'],
            'filters_applied' => array_filter($filters),
            'total_records' => count($tasks),
            'export_version' => '4.0'
        ],
        'statistics' => $stats,
        'tasks' => $tasks,
        'summary' => [
            'by_status' => [
                'completed' => $stats['completed'],
                'in_progress' => $stats['in_progress'],
                'pending' => $stats['pending'],
                'cancelled' => $stats['cancelled']
            ],
            'by_priority' => [
                'urgent' => $stats['urgent_count'],
                'high' => $stats['high_count'],
                'medium' => $stats['medium_count'],
                'low' => $stats['low_count']
            ]
        ]
    ];

    $filename = 'tasks_report_' . date('Y-m-d_H-i-s') . '.json';

    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

/**
 * تصدير إلى PDF محسن
 */
function exportToPDF($tasks, $stats, $filters) {
    $html = generateAdvancedPDFHTML($tasks, $stats, $filters);

    $filename = 'tasks_report_' . date('Y-m-d_H-i-s') . '.pdf';

    // محاولة استخدام مكتبة PDF إذا كانت متوفرة
    if (class_exists('TCPDF')) {
        generateTCPDF($html, $filename);
    } elseif (class_exists('Dompdf\Dompdf')) {
        generateDomPDF($html, $filename);
    } else {
        // fallback إلى HTML
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . str_replace('.pdf', '.html', $filename) . '"');
        echo $html;
    }
}

/**
 * تسجيل نشاط التصدير
 */
function logExportActivity($userId, $exportType, $recordCount) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            INSERT INTO activity_log (user_id, action, details, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $details = json_encode([
            'export_type' => $exportType,
            'record_count' => $recordCount,
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        $stmt->execute([
            $userId,
            'تصدير المهام',
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log("خطأ في تسجيل نشاط التصدير: " . $e->getMessage());
    }
}

/**
 * تنسيق التاريخ
 */
function formatDate($date) {
    if (!$date) return '';
    return date('d/m/Y', strtotime($date));
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime($datetime) {
    if (!$datetime) return '';
    return date('d/m/Y H:i', strtotime($datetime));
}
