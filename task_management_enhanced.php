<?php
/**
 * نظام إدارة المهام المحسن
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
$userId = $_SESSION['user_id'];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handlePostActions($conn, $isAdmin, $userId);
}

// الحصول على معاملات الفلترة
$filters = getFilterParameters();

// جلب المهام
$tasksData = getTasksWithFilters($conn, $filters);
$tasks = $tasksData['tasks'];
$totalTasks = $tasksData['total'];
$totalPages = $tasksData['totalPages'];

// جلب الإحصائيات
$stats = getTaskStatistics($conn, $filters);

// جلب البيانات المساعدة
$users = getUsers($conn);
$categories = getCategories($conn);

/**
 * معالجة إجراءات POST
 */
function handlePostActions($conn, $isAdmin, $userId) {
    try {
        if (isset($_POST['add_task']) && $isAdmin) {
            addNewTask($conn, $_POST, $userId);
            $_SESSION['success'] = "تمت إضافة المهمة بنجاح.";
        }

        if (isset($_POST['update_task']) && $isAdmin) {
            updateTask($conn, $_POST, $userId);
            $_SESSION['success'] = "تم تحديث المهمة بنجاح.";
        }

        if (isset($_POST['delete_task']) && $isAdmin) {
            deleteTask($conn, $_POST['task_id'], $userId);
            $_SESSION['success'] = "تم حذف المهمة بنجاح.";
        }

        if (isset($_POST['update_status'])) {
            updateTaskStatus($conn, $_POST, $userId);
            $_SESSION['success'] = "تم تحديث حالة المهمة بنجاح.";
        }

        if (isset($_POST['add_comment'])) {
            addTaskComment($conn, $_POST, $userId);
            $_SESSION['success'] = "تم إضافة التعليق بنجاح.";
        }

        // إعادة توجيه لتجنب إعادة الإرسال
        header("Location: " . $_SERVER['PHP_SELF'] . "?" . http_build_query($_GET));
        exit;

    } catch (Exception $e) {
        error_log("خطأ في معالجة الإجراء: " . $e->getMessage());
        $_SESSION['error'] = "حدث خطأ في معالجة الطلب.";
    }
}

/**
 * إضافة مهمة جديدة
 */
function addNewTask($conn, $data, $userId) {
    $stmt = $conn->prepare("
        INSERT INTO notifications (
            Title, Message, NotifyDate, ToDate, Status, Priority,
            Category, AssignedTo, Progress, Notes, CreatedBy
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $data['title'],
        $data['message'],
        $data['notify_date'],
        $data['end_date'] ?? $data['notify_date'],
        $data['status'] ?? 'لم تنفذ',
        $data['priority'] ?? 'متوسطة',
        $data['category'] ?? null,
        $data['assigned_to'] ?? null,
        intval($data['progress'] ?? 0),
        $data['notes'] ?? null,
        $userId
    ]);

    $taskId = $conn->lastInsertId();

    // تسجيل في سجل التغييرات
    logTaskHistory($conn, $taskId, $userId, 'إنشاء', null, 'تم إنشاء المهمة');
}

/**
 * تحديث مهمة
 */
function updateTask($conn, $data, $userId) {
    // جلب البيانات القديمة للمقارنة
    $oldTask = getTaskById($conn, $data['task_id']);

    $stmt = $conn->prepare("
        UPDATE notifications SET
            Title = ?, Message = ?, NotifyDate = ?, ToDate = ?,
            Status = ?, Priority = ?, Category = ?, AssignedTo = ?,
            Progress = ?, Notes = ?, LastUpdated = CURRENT_TIMESTAMP
        WHERE ID = ?
    ");

    $stmt->execute([
        $data['title'],
        $data['message'],
        $data['notify_date'],
        $data['end_date'],
        $data['status'],
        $data['priority'],
        $data['category'],
        $data['assigned_to'],
        intval($data['progress']),
        $data['notes'],
        $data['task_id']
    ]);

    // تسجيل التغييرات
    logTaskChanges($conn, $data['task_id'], $userId, $oldTask, $data);
}

/**
 * حذف مهمة
 */
function deleteTask($conn, $taskId, $userId) {
    // حذف التعليقات والسجلات المرتبطة أولاً
    $conn->prepare("DELETE FROM task_comments WHERE TaskID = ?")->execute([$taskId]);
    $conn->prepare("DELETE FROM task_history WHERE TaskID = ?")->execute([$taskId]);

    // حذف المهمة
    $conn->prepare("DELETE FROM notifications WHERE ID = ?")->execute([$taskId]);

    // تسجيل الحذف
    logTaskHistory($conn, $taskId, $userId, 'حذف', null, 'تم حذف المهمة');
}

/**
 * تحديث حالة المهمة
 */
function updateTaskStatus($conn, $data, $userId) {
    $oldStatus = getTaskStatus($conn, $data['task_id']);

    $stmt = $conn->prepare("
        UPDATE notifications SET
            Status = ?,
            Progress = CASE
                WHEN ? = 'تم' THEN 100
                WHEN ? = 'تحت التنفيذ' AND Progress = 0 THEN 25
                ELSE Progress
            END,
            LastUpdated = CURRENT_TIMESTAMP
        WHERE ID = ?
    ");

    $stmt->execute([
        $data['status'],
        $data['status'],
        $data['status'],
        $data['task_id']
    ]);

    // تسجيل التغيير
    logTaskHistory($conn, $data['task_id'], $userId, 'تحديث الحالة', $oldStatus, $data['status']);
}

/**
 * إضافة تعليق على المهمة
 */
function addTaskComment($conn, $data, $userId) {
    $stmt = $conn->prepare("
        INSERT INTO task_comments (TaskID, UserID, Comment)
        VALUES (?, ?, ?)
    ");

    $stmt->execute([
        $data['task_id'],
        $userId,
        $data['comment']
    ]);
}

/**
 * الحصول على معاملات الفلترة
 */
function getFilterParameters() {
    return [
        'search' => trim($_GET['search'] ?? ''),
        'status' => $_GET['status'] ?? '',
        'priority' => $_GET['priority'] ?? '',
        'category' => $_GET['category'] ?? '',
        'assigned_to' => $_GET['assigned_to'] ?? '',
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'sort_by' => $_GET['sort_by'] ?? 'NotifyDate',
        'sort_order' => $_GET['sort_order'] ?? 'ASC',
        'page' => max(1, intval($_GET['page'] ?? 1)),
        'limit' => 50
    ];
}

/**
 * جلب المهام مع الفلاتر
 */
function getTasksWithFilters($conn, $filters) {
    $query = buildTaskQuery($filters);

    $stmt = $conn->prepare($query['sql']);
    $stmt->execute($query['params']);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب العدد الإجمالي
    $countQuery = buildCountQuery($filters);
    $countStmt = $conn->prepare($countQuery['sql']);
    $countStmt->execute($countQuery['params']);
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

    return [
        'tasks' => $tasks,
        'total' => $total,
        'totalPages' => ceil($total / $filters['limit'])
    ];
}

/**
 * بناء استعلام المهام
 */
function buildTaskQuery($filters) {
    $sql = "SELECT
        n.ID, n.Title, n.Message, n.NotifyDate, n.ToDate, n.Status,
        n.Priority, n.Category, n.Progress, n.Notes, n.CreatedAt, n.LastUpdated,
        u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
        u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
        tc.Color as CategoryColor, tc.Icon as CategoryIcon,

        -- حسابات الحالة
        CASE
            WHEN n.Status = 'تم' THEN 'completed'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 'overdue'
            WHEN n.NotifyDate = CURDATE() THEN 'today'
            ELSE 'normal'
        END as row_class,

        -- الأيام المتبقية
        CASE
            WHEN n.Status = 'تم' THEN 'مكتمل'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN CONCAT('متأخر ', DATEDIFF(CURDATE(), n.ToDate), ' يوم')
            WHEN n.ToDate = CURDATE() THEN 'ينتهي اليوم'
            WHEN n.ToDate > CURDATE() THEN CONCAT(DATEDIFF(n.ToDate, CURDATE()), ' يوم متبقي')
            ELSE ''
        END as days_remaining,

        -- عدد التعليقات
        (SELECT COUNT(*) FROM task_comments tc WHERE tc.TaskID = n.ID) as comments_count

    FROM notifications n
    LEFT JOIN users u1 ON n.CreatedBy = u1.ID
    LEFT JOIN users u2 ON n.AssignedTo = u2.ID
    LEFT JOIN task_categories tc ON n.Category = tc.Name";

    $conditions = [];
    $params = [];

    // فلاتر البحث
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['status'])) {
        $conditions[] = "n.Status = ?";
        $params[] = $filters['status'];
    }

    if (!empty($filters['priority'])) {
        $conditions[] = "n.Priority = ?";
        $params[] = $filters['priority'];
    }

    if (!empty($filters['category'])) {
        $conditions[] = "n.Category = ?";
        $params[] = $filters['category'];
    }

    if (!empty($filters['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $params[] = $filters['assigned_to'];
    }

    if (!empty($filters['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $params[] = $filters['date_from'];
    }

    if (!empty($filters['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $params[] = $filters['date_to'];
    }

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    return [
        'sql' => $sql,
        'params' => $params
    ];
}

/**
 * جلب إحصائيات المهام
 */
function getTaskStatistics($conn, $filters) {
    $whereClause = '';
    $params = [];

    // تطبيق نفس فلاتر البحث على الإحصائيات
    $conditions = [];

    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(Title LIKE ? OR Message LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['status'])) {
        $conditions[] = "Status = ?";
        $params[] = $filters['status'];
    }

    if (!empty($filters['priority'])) {
        $conditions[] = "Priority = ?";
        $params[] = $filters['priority'];
    }

    if (!empty($filters['category'])) {
        $conditions[] = "Category = ?";
        $params[] = $filters['category'];
    }

    if (!empty($conditions)) {
        $whereClause = " WHERE " . implode(" AND ", $conditions);
    }

    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            SUM(CASE WHEN NotifyDate = CURDATE() THEN 1 ELSE 0 END) as today,
            AVG(Progress) as avg_progress
        FROM notifications
        $whereClause
    ");

    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    // حساب معدل الإنجاز
    if ($result['total'] > 0) {
        $result['completion_rate'] = round(($result['completed'] / $result['total']) * 100, 1);
    } else {
        $result['completion_rate'] = 0;
    }

    $result['avg_progress'] = round($result['avg_progress'], 1);

    return $result;
}

/**
 * جلب المستخدمين
 */
function getUsers($conn) {
    $stmt = $conn->prepare("
        SELECT ID, Username, FullName
        FROM users
        WHERE IsActive = 1
        ORDER BY FullName, Username
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * جلب التصنيفات
 */
function getCategories($conn) {
    $stmt = $conn->prepare("
        SELECT Name, Description, Color, Icon
        FROM task_categories
        WHERE IsActive = 1
        ORDER BY SortOrder, Name
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * جلب مهمة بالمعرف
 */
function getTaskById($conn, $taskId) {
    $stmt = $conn->prepare("SELECT * FROM notifications WHERE ID = ?");
    $stmt->execute([$taskId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * جلب حالة المهمة
 */
function getTaskStatus($conn, $taskId) {
    $stmt = $conn->prepare("SELECT Status FROM notifications WHERE ID = ?");
    $stmt->execute([$taskId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['Status'] : null;
}

/**
 * تسجيل تاريخ المهمة
 */
function logTaskHistory($conn, $taskId, $userId, $action, $oldValue = null, $newValue = null) {
    $stmt = $conn->prepare("
        INSERT INTO task_history (TaskID, UserID, Action, OldValue, NewValue, IPAddress, UserAgent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $taskId,
        $userId,
        $action,
        $oldValue,
        $newValue,
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
}

/**
 * تسجيل تغييرات المهمة
 */
function logTaskChanges($conn, $taskId, $userId, $oldTask, $newData) {
    $fields = [
        'Title' => 'العنوان',
        'Message' => 'الوصف',
        'NotifyDate' => 'تاريخ البداية',
        'ToDate' => 'تاريخ الانتهاء',
        'Status' => 'الحالة',
        'Priority' => 'الأولوية',
        'Category' => 'التصنيف',
        'AssignedTo' => 'المسؤول',
        'Progress' => 'التقدم',
        'Notes' => 'الملاحظات'
    ];

    foreach ($fields as $field => $fieldName) {
        $oldValue = $oldTask[$field] ?? '';
        $newValue = $newData[strtolower($field)] ?? $newData[$field] ?? '';

        if ($oldValue != $newValue) {
            logTaskHistory($conn, $taskId, $userId, "تحديث $fieldName", $oldValue, $newValue);
        }
    }
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate($date) {
    if (!$date) return '';
    return date('d/m/Y', strtotime($date));
}

/**
 * تنسيق التاريخ والوقت للعرض
 */
function formatDateTime($datetime) {
    if (!$datetime) return '';
    return date('d/m/Y H:i', strtotime($datetime));
}

/**
 * الحصول على كلاس الأولوية
 */
function getPriorityClass($priority) {
    $classes = [
        'منخفضة' => 'priority-low',
        'متوسطة' => 'priority-medium',
        'عالية' => 'priority-high',
        'عاجلة' => 'priority-urgent'
    ];
    return $classes[$priority] ?? 'priority-medium';
}

/**
 * الحصول على أيقونة الأولوية
 */
function getPriorityIcon($priority) {
    $icons = [
        'منخفضة' => 'fas fa-arrow-down',
        'متوسطة' => 'fas fa-minus',
        'عالية' => 'fas fa-arrow-up',
        'عاجلة' => 'fas fa-exclamation-triangle'
    ];
    return $icons[$priority] ?? 'fas fa-minus';
}

/**
 * الحصول على كلاس الحالة
 */
function getStatusClass($status) {
    $classes = [
        'تم' => 'status-completed',
        'تحت التنفيذ' => 'status-in-progress',
        'لم تنفذ' => 'status-pending',
        'ملغي' => 'status-cancelled'
    ];
    return $classes[$status] ?? 'status-pending';
}

/**
 * الحصول على أيقونة الحالة
 */
function getStatusIcon($status) {
    $icons = [
        'تم' => 'fas fa-check-circle',
        'تحت التنفيذ' => 'fas fa-clock',
        'لم تنفذ' => 'fas fa-hourglass-half',
        'ملغي' => 'fas fa-times-circle'
    ];
    return $icons[$status] ?? 'fas fa-hourglass-half';
}

/**
 * الحصول على كلاس التقدم
 */
function getProgressClass($progress) {
    if ($progress >= 80) return 'progress-high';
    if ($progress >= 50) return 'progress-medium';
    if ($progress >= 20) return 'progress-low';
    return 'progress-none';
}

/**
 * تقصير النص
 */
function truncateText($text, $length = 50) {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length) . '...';
}

/**
 * تنظيف وتأمين المدخلات
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة التاريخ
 */
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * إنشاء رابط الفلترة
 */
function buildFilterUrl($filters, $newFilters = []) {
    $mergedFilters = array_merge($filters, $newFilters);

    // إزالة الفلاتر الفارغة
    $mergedFilters = array_filter($mergedFilters, function($value) {
        return $value !== '' && $value !== null;
    });

    return '?' . http_build_query($mergedFilters);
}

/**
 * إنشاء رابط الترتيب
 */
function buildSortUrl($filters, $sortBy) {
    $newOrder = ($filters['sort_by'] === $sortBy && $filters['sort_order'] === 'ASC') ? 'DESC' : 'ASC';

    return buildFilterUrl($filters, [
        'sort_by' => $sortBy,
        'sort_order' => $newOrder
    ]);
}

/**
 * الحصول على أيقونة الترتيب
 */
function getSortIcon($filters, $column) {
    if ($filters['sort_by'] !== $column) {
        return '<i class="fas fa-sort text-muted"></i>';
    }

    $icon = $filters['sort_order'] === 'ASC' ? 'fa-sort-up' : 'fa-sort-down';
    return '<i class="fas ' . $icon . ' text-primary"></i>';
}

// تسجيل زيارة الصفحة
if (isset($_SESSION['user_id'])) {
    logActivity($conn, $_SESSION['user_id'], basename(__FILE__), 'زيارة الصفحة', $_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'], json_encode($_GET), null);
}
?>

/**
 * بناء استعلام العد
 */
function buildCountQuery($filters) {
    $sql = "SELECT COUNT(*) as total FROM notifications n";

    $conditions = [];
    $params = [];

    // نفس شروط الفلترة
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['status'])) {
        $conditions[] = "n.Status = ?";
        $params[] = $filters['status'];
    }

    if (!empty($filters['priority'])) {
        $conditions[] = "n.Priority = ?";
        $params[] = $filters['priority'];
    }

    if (!empty($filters['category'])) {
        $conditions[] = "n.Category = ?";
        $params[] = $filters['category'];
    }

    if (!empty($filters['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $params[] = $filters['assigned_to'];
    }

    if (!empty($filters['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $params[] = $filters['date_from'];
    }

    if (!empty($filters['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $params[] = $filters['date_to'];
    }

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    return [
        'sql' => $sql,
        'params' => $params
    ];
}
