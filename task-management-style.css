/* ===== نظام إدارة المهام المحسن - التصميم العصري ===== */
/* الإصدار: 4.0 | التاريخ: 2025-07-14 | المطور: Augment Agent */

/* ===== المتغيرات العامة ===== */
:root {
    /* الألوان الأساسية - حسب تفضيلات المستخدم */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white-color: #ffffff;
    --black-color: #000000;

    /* ألوان الخلفية */
    --bg-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
    --card-bg: rgba(255, 255, 255, 0.95);
    --sidebar-bg: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);

    /* الظلال */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);

    /* الانتقالات */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* الخطوط */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;

    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* الحدود */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
}

/* ===== التصميم العام ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background: var(--bg-gradient);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* ===== الحاوي الرئيسي ===== */
.task-management-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
}

/* ===== الشريط العلوي ===== */
.header-section {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-title {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.header-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    border-radius: 2px;
}

/* ===== شريط التنقل والفلاتر ===== */
.navigation-bar {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.month-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.nav-button {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--white-color);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.nav-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-button:hover::before {
    left: 100%;
}

.current-month {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(23, 162, 184, 0.1));
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(0, 123, 255, 0.2);
}

/* ===== نظام البحث والفلترة ===== */
.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.filter-group {
    position: relative;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(0, 123, 255, 0.2);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    background: var(--white-color);
    transition: var(--transition-normal);
    outline: none;
}

.filter-input:focus,
.filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.filter-label {
    position: absolute;
    top: -8px;
    right: 12px;
    background: var(--white-color);
    padding: 0 var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

/* ===== أزرار الإجراءات ===== */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-lg);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--white-color);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: var(--white-color);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #fd7e14);
    color: var(--dark-color);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e83e8c);
    color: var(--white-color);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #495057);
    color: var(--white-color);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* ===== لوحة الإحصائيات ===== */
.stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

/* ===== النماذج والمودال ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-normal);
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid rgba(0, 123, 255, 0.1);
}

.modal-title {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition-fast);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
}

.modal-close:hover {
    color: var(--danger-color);
    background: rgba(220, 53, 69, 0.1);
}

/* ===== نماذج الإدخال ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--dark-color);
    font-size: var(--font-size-base);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(0, 123, 255, 0.2);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    background: var(--white-color);
    transition: var(--transition-normal);
    outline: none;
    font-family: var(--font-family);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.form-input:invalid,
.form-select:invalid,
.form-textarea:invalid {
    border-color: var(--danger-color);
}

/* ===== شريط التقدم ===== */
.progress-container {
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
    height: 8px;
    overflow: hidden;
    margin: var(--spacing-xs) 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #20c997);
    border-radius: var(--border-radius-sm);
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== التعليقات ===== */
.comments-section {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 2px solid rgba(0, 123, 255, 0.1);
}

.comment-item {
    background: rgba(0, 123, 255, 0.05);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-right: 4px solid var(--primary-color);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.comment-author {
    font-weight: 600;
    color: var(--primary-color);
}

.comment-date {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.comment-text {
    color: var(--dark-color);
    line-height: 1.6;
}

/* ===== التنبيهات والرسائل ===== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    border-color: var(--warning-color);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(0, 123, 255, 0.1));
    border-color: var(--info-color);
    color: var(--info-color);
}

/* ===== شاشة التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: var(--transition-slow);
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 123, 255, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    text-align: center;
}

/* ===== التحسينات الإضافية ===== */
.smooth-scroll {
    scroll-behavior: smooth;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .task-management-container {
        max-width: none !important;
        padding: 0 !important;
    }

    .tasks-table {
        border-collapse: collapse;
    }

    .tasks-table th,
    .tasks-table td {
        border: 1px solid #000 !important;
        padding: 8px !important;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== تحسينات الأداء ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
.stat-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 600;
}

/* ===== جدول المهام ===== */
.tasks-table-container {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table-responsive {
    overflow-x: auto;
    max-height: 70vh;
}

.tasks-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-base);
}

.tasks-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--white-color);
    padding: var(--spacing-lg);
    text-align: right;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.tasks-table td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    vertical-align: middle;
}

.task-row {
    transition: var(--transition-normal);
    background: var(--white-color);
}

.task-row:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05), rgba(23, 162, 184, 0.05));
    transform: scale(1.01);
    box-shadow: var(--shadow-md);
}

/* ===== حالات المهام ===== */
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-normal);
}

.status-completed {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: var(--white-color);
}

.status-in-progress {
    background: linear-gradient(135deg, var(--warning-color), #fd7e14);
    color: var(--dark-color);
}

.status-pending {
    background: linear-gradient(135deg, var(--secondary-color), #495057);
    color: var(--white-color);
}

.status-overdue {
    background: linear-gradient(135deg, var(--danger-color), #e83e8c);
    color: var(--white-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* ===== الأولوية ===== */
.priority-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.priority-low {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: var(--white-color);
}

.priority-medium {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: var(--dark-color);
}

.priority-high {
    background: linear-gradient(135deg, #fd7e14, #dc3545);
    color: var(--white-color);
}

.priority-urgent {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: var(--white-color);
    animation: pulse 2s infinite;
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .task-management-container {
        padding: var(--spacing-md);
    }

    .header-title {
        font-size: 1.5rem;
    }

    .month-navigation {
        flex-direction: column;
        text-align: center;
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .stats-dashboard {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
        justify-content: center;
    }

    .tasks-table {
        font-size: var(--font-size-sm);
    }

    .tasks-table th,
    .tasks-table td {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .stats-dashboard {
        grid-template-columns: 1fr;
    }

    .tasks-table th,
    .tasks-table td {
        padding: var(--spacing-xs);
        font-size: 0.75rem;
    }
}
