<?php
/**
 * نظام البحث والفلترة المتقدم للمهام
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    // الحصول على معاملات البحث والفلترة
    $searchParams = getSearchParameters();
    
    // بناء الاستعلام
    $query = buildSearchQuery($searchParams);
    
    // تنفيذ الاستعلام
    $stmt = $conn->prepare($query['sql']);
    $stmt->execute($query['params']);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب العدد الإجمالي
    $totalCount = getTotalCount($conn, $searchParams);
    
    // حساب الإحصائيات
    $stats = calculateStats($conn, $searchParams);
    
    // تنسيق النتائج
    $formattedTasks = formatTasks($tasks);
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'tasks' => $formattedTasks,
        'total' => $totalCount,
        'stats' => $stats,
        'page' => $searchParams['page'],
        'totalPages' => ceil($totalCount / $searchParams['limit']),
        'filters' => $searchParams
    ]);

} catch (Exception $e) {
    error_log("خطأ في البحث: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في البحث',
        'error' => $e->getMessage()
    ]);
}

/**
 * الحصول على معاملات البحث والفلترة
 */
function getSearchParameters() {
    $params = [
        // البحث النصي
        'search' => trim($_GET['search'] ?? ''),
        
        // فلاتر الحالة
        'status' => $_GET['status'] ?? '',
        'priority' => $_GET['priority'] ?? '',
        'category' => $_GET['category'] ?? '',
        
        // فلاتر المستخدمين
        'assigned_to' => $_GET['assigned_to'] ?? '',
        'created_by' => $_GET['created_by'] ?? '',
        
        // فلاتر التاريخ
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'created_from' => $_GET['created_from'] ?? '',
        'created_to' => $_GET['created_to'] ?? '',
        
        // فلاتر التقدم
        'progress_min' => intval($_GET['progress_min'] ?? 0),
        'progress_max' => intval($_GET['progress_max'] ?? 100),
        
        // الترتيب والصفحات
        'sort_by' => $_GET['sort_by'] ?? 'NotifyDate',
        'sort_order' => strtoupper($_GET['sort_order'] ?? 'ASC'),
        'page' => max(1, intval($_GET['page'] ?? 1)),
        'limit' => min(100, max(10, intval($_GET['limit'] ?? 50))),
        
        // فلاتر سريعة
        'quick_filter' => $_GET['quick_filter'] ?? '',
        
        // فلاتر متقدمة
        'has_attachments' => $_GET['has_attachments'] ?? '',
        'has_comments' => $_GET['has_comments'] ?? '',
        'overdue_only' => $_GET['overdue_only'] ?? '',
        'today_only' => $_GET['today_only'] ?? ''
    ];
    
    // تنظيف المعاملات
    $params = array_map(function($value) {
        return is_string($value) ? trim($value) : $value;
    }, $params);
    
    return $params;
}

/**
 * بناء استعلام البحث
 */
function buildSearchQuery($params) {
    $sql = "SELECT 
        n.ID, n.Title, n.Message, n.NotifyDate, n.ToDate, n.Status,
        n.Priority, n.Category, n.Progress, n.Notes, n.CreatedAt, n.LastUpdated,
        u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
        u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
        tc.Color as CategoryColor, tc.Icon as CategoryIcon,
        
        -- حسابات إضافية
        CASE
            WHEN n.Status = 'تم' THEN 'completed'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN 'overdue'
            WHEN n.NotifyDate = CURDATE() THEN 'today'
            WHEN n.ToDate = CURDATE() THEN 'end_today'
            ELSE 'normal'
        END as row_class,
        
        CASE
            WHEN n.Status = 'تم' THEN 'مكتمل'
            WHEN n.ToDate < CURDATE() AND n.Status != 'تم' THEN CONCAT('متأخر ', DATEDIFF(CURDATE(), n.ToDate), ' يوم')
            WHEN n.ToDate = CURDATE() THEN 'ينتهي اليوم'
            WHEN n.ToDate > CURDATE() THEN CONCAT(DATEDIFF(n.ToDate, CURDATE()), ' يوم متبقي')
            ELSE ''
        END as days_remaining,
        
        -- عدد التعليقات
        (SELECT COUNT(*) FROM task_comments tc WHERE tc.TaskID = n.ID) as comments_count,
        
        -- وجود مرفقات
        CASE WHEN n.Attachments IS NOT NULL AND n.Attachments != '' THEN 1 ELSE 0 END as has_attachments
        
    FROM notifications n
    LEFT JOIN users u1 ON n.CreatedBy = u1.ID
    LEFT JOIN users u2 ON n.AssignedTo = u2.ID
    LEFT JOIN task_categories tc ON n.Category = tc.Name";
    
    $conditions = [];
    $queryParams = [];
    
    // شروط البحث النصي
    if (!empty($params['search'])) {
        $searchTerm = '%' . $params['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ? OR n.Notes LIKE ?)";
        $queryParams[] = $searchTerm;
        $queryParams[] = $searchTerm;
        $queryParams[] = $searchTerm;
    }
    
    // فلتر الحالة
    if (!empty($params['status'])) {
        $conditions[] = "n.Status = ?";
        $queryParams[] = $params['status'];
    }
    
    // فلتر الأولوية
    if (!empty($params['priority'])) {
        $conditions[] = "n.Priority = ?";
        $queryParams[] = $params['priority'];
    }
    
    // فلتر التصنيف
    if (!empty($params['category'])) {
        $conditions[] = "n.Category = ?";
        $queryParams[] = $params['category'];
    }
    
    // فلتر المسؤول
    if (!empty($params['assigned_to'])) {
        $conditions[] = "n.AssignedTo = ?";
        $queryParams[] = $params['assigned_to'];
    }
    
    // فلتر المنشئ
    if (!empty($params['created_by'])) {
        $conditions[] = "n.CreatedBy = ?";
        $queryParams[] = $params['created_by'];
    }
    
    // فلاتر التاريخ
    if (!empty($params['date_from'])) {
        $conditions[] = "n.NotifyDate >= ?";
        $queryParams[] = $params['date_from'];
    }
    
    if (!empty($params['date_to'])) {
        $conditions[] = "n.ToDate <= ?";
        $queryParams[] = $params['date_to'];
    }
    
    if (!empty($params['created_from'])) {
        $conditions[] = "DATE(n.CreatedAt) >= ?";
        $queryParams[] = $params['created_from'];
    }
    
    if (!empty($params['created_to'])) {
        $conditions[] = "DATE(n.CreatedAt) <= ?";
        $queryParams[] = $params['created_to'];
    }
    
    // فلاتر التقدم
    if ($params['progress_min'] > 0) {
        $conditions[] = "n.Progress >= ?";
        $queryParams[] = $params['progress_min'];
    }
    
    if ($params['progress_max'] < 100) {
        $conditions[] = "n.Progress <= ?";
        $queryParams[] = $params['progress_max'];
    }
    
    // الفلاتر السريعة
    switch ($params['quick_filter']) {
        case 'today':
            $conditions[] = "n.NotifyDate = CURDATE()";
            break;
        case 'overdue':
            $conditions[] = "n.ToDate < CURDATE() AND n.Status != 'تم'";
            break;
        case 'this_week':
            $conditions[] = "YEARWEEK(n.NotifyDate, 1) = YEARWEEK(CURDATE(), 1)";
            break;
        case 'this_month':
            $conditions[] = "YEAR(n.NotifyDate) = YEAR(CURDATE()) AND MONTH(n.NotifyDate) = MONTH(CURDATE())";
            break;
        case 'high_priority':
            $conditions[] = "n.Priority IN ('عالية', 'عاجلة')";
            break;
    }
    
    // فلاتر متقدمة
    if ($params['has_attachments'] === '1') {
        $conditions[] = "n.Attachments IS NOT NULL AND n.Attachments != ''";
    } elseif ($params['has_attachments'] === '0') {
        $conditions[] = "(n.Attachments IS NULL OR n.Attachments = '')";
    }
    
    if ($params['has_comments'] === '1') {
        $conditions[] = "EXISTS (SELECT 1 FROM task_comments tc WHERE tc.TaskID = n.ID)";
    } elseif ($params['has_comments'] === '0') {
        $conditions[] = "NOT EXISTS (SELECT 1 FROM task_comments tc WHERE tc.TaskID = n.ID)";
    }
    
    if ($params['overdue_only'] === '1') {
        $conditions[] = "n.ToDate < CURDATE() AND n.Status != 'تم'";
    }
    
    if ($params['today_only'] === '1') {
        $conditions[] = "n.NotifyDate = CURDATE()";
    }
    
    // إضافة الشروط للاستعلام
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    // الترتيب
    $allowedSortColumns = ['NotifyDate', 'ToDate', 'Title', 'Status', 'Priority', 'Progress', 'CreatedAt', 'LastUpdated'];
    $allowedSortOrders = ['ASC', 'DESC'];
    
    if (in_array($params['sort_by'], $allowedSortColumns) && in_array($params['sort_order'], $allowedSortOrders)) {
        $sql .= " ORDER BY n.{$params['sort_by']} {$params['sort_order']}";
    } else {
        $sql .= " ORDER BY n.NotifyDate ASC";
    }
    
    // الحد والإزاحة
    $offset = ($params['page'] - 1) * $params['limit'];
    $sql .= " LIMIT {$params['limit']} OFFSET {$offset}";
    
    return [
        'sql' => $sql,
        'params' => $queryParams
    ];
}

/**
 * حساب العدد الإجمالي للنتائج
 */
function getTotalCount($conn, $params) {
    $countSql = "SELECT COUNT(*) as total FROM notifications n";
    
    $conditions = [];
    $queryParams = [];
    
    // نفس شروط البحث الأساسية (بدون JOIN للأداء)
    if (!empty($params['search'])) {
        $searchTerm = '%' . $params['search'] . '%';
        $conditions[] = "(n.Title LIKE ? OR n.Message LIKE ? OR n.Notes LIKE ?)";
        $queryParams[] = $searchTerm;
        $queryParams[] = $searchTerm;
        $queryParams[] = $searchTerm;
    }
    
    if (!empty($params['status'])) {
        $conditions[] = "n.Status = ?";
        $queryParams[] = $params['status'];
    }
    
    if (!empty($params['priority'])) {
        $conditions[] = "n.Priority = ?";
        $queryParams[] = $params['priority'];
    }
    
    if (!empty($params['category'])) {
        $conditions[] = "n.Category = ?";
        $queryParams[] = $params['category'];
    }
    
    // إضافة باقي الشروط...
    // (يمكن تحسين هذا بإنشاء دالة مشتركة)
    
    if (!empty($conditions)) {
        $countSql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    $stmt = $conn->prepare($countSql);
    $stmt->execute($queryParams);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return intval($result['total']);
}

/**
 * حساب الإحصائيات
 */
function calculateStats($conn, $params) {
    $statsSql = "SELECT
        COUNT(*) as total,
        SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
        SUM(CASE WHEN NotifyDate = CURDATE() THEN 1 ELSE 0 END) as today,
        AVG(Progress) as avg_progress
    FROM notifications n";
    
    // يمكن إضافة نفس شروط البحث هنا للحصول على إحصائيات مفلترة
    
    $stmt = $conn->prepare($statsSql);
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // حساب معدل الإنجاز
    if ($stats['total'] > 0) {
        $stats['completion_rate'] = round(($stats['completed'] / $stats['total']) * 100, 1);
    } else {
        $stats['completion_rate'] = 0;
    }
    
    $stats['avg_progress'] = round($stats['avg_progress'], 1);
    
    return $stats;
}

/**
 * تنسيق المهام للعرض
 */
function formatTasks($tasks) {
    return array_map(function($task) {
        // تنسيق التواريخ
        $task['NotifyDate_formatted'] = date('d/m/Y', strtotime($task['NotifyDate']));
        $task['ToDate_formatted'] = $task['ToDate'] ? date('d/m/Y', strtotime($task['ToDate'])) : '';
        $task['CreatedAt_formatted'] = date('d/m/Y H:i', strtotime($task['CreatedAt']));
        
        // تنسيق الأولوية
        $task['priority_class'] = getPriorityClass($task['Priority']);
        $task['priority_icon'] = getPriorityIcon($task['Priority']);
        
        // تنسيق الحالة
        $task['status_class'] = getStatusClass($task['Status']);
        $task['status_icon'] = getStatusIcon($task['Status']);
        
        // تنسيق التقدم
        $task['progress_class'] = getProgressClass($task['Progress']);
        
        // تقصير النص الطويل
        $task['Title_short'] = mb_substr($task['Title'], 0, 50) . (mb_strlen($task['Title']) > 50 ? '...' : '');
        $task['Message_short'] = mb_substr($task['Message'], 0, 100) . (mb_strlen($task['Message']) > 100 ? '...' : '');
        
        return $task;
    }, $tasks);
}

// دوال مساعدة للتنسيق
function getPriorityClass($priority) {
    $classes = [
        'منخفضة' => 'priority-low',
        'متوسطة' => 'priority-medium',
        'عالية' => 'priority-high',
        'عاجلة' => 'priority-urgent'
    ];
    return $classes[$priority] ?? 'priority-medium';
}

function getPriorityIcon($priority) {
    $icons = [
        'منخفضة' => 'fas fa-arrow-down',
        'متوسطة' => 'fas fa-minus',
        'عالية' => 'fas fa-arrow-up',
        'عاجلة' => 'fas fa-exclamation-triangle'
    ];
    return $icons[$priority] ?? 'fas fa-minus';
}

function getStatusClass($status) {
    $classes = [
        'تم' => 'status-completed',
        'تحت التنفيذ' => 'status-in-progress',
        'لم تنفذ' => 'status-pending',
        'ملغي' => 'status-cancelled'
    ];
    return $classes[$status] ?? 'status-pending';
}

function getStatusIcon($status) {
    $icons = [
        'تم' => 'fas fa-check-circle',
        'تحت التنفيذ' => 'fas fa-clock',
        'لم تنفذ' => 'fas fa-hourglass-half',
        'ملغي' => 'fas fa-times-circle'
    ];
    return $icons[$status] ?? 'fas fa-hourglass-half';
}

function getProgressClass($progress) {
    if ($progress >= 80) return 'progress-high';
    if ($progress >= 50) return 'progress-medium';
    if ($progress >= 20) return 'progress-low';
    return 'progress-none';
}
?>
