<?php
/**
 * واجهة نظام إدارة المهام المحسن
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

// تضمين الملف الرئيسي
require_once 'task_management_enhanced.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المهام المحسن</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- التصميم المخصص -->
    <link href="task-management-style.css" rel="stylesheet">

    <!-- Animate.css للتأثيرات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري التحميل...</div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="task-management-container">

        <!-- الشريط العلوي -->
        <header class="header-section animate__animated animate__fadeInDown">
            <h1 class="header-title">
                <i class="fas fa-tasks me-3"></i>
                نظام إدارة المهام المحسن
            </h1>
            <div class="text-center text-muted">
                <small>إدارة شاملة ومتقدمة للمهام والمشاريع</small>
            </div>
        </header>

        <!-- عرض الرسائل -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($_SESSION['success']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($_SESSION['error']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- لوحة الإحصائيات -->
        <section class="stats-dashboard animate__animated animate__fadeInUp">
            <div class="stat-card" data-filter="total" data-stat="total">
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-number"><?= $stats['total'] ?></div>
                <div class="stat-label">إجمالي المهام</div>
            </div>

            <div class="stat-card" data-filter="completed" data-stat="completed">
                <div class="stat-icon text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?= $stats['completed'] ?></div>
                <div class="stat-label">مكتملة</div>
            </div>

            <div class="stat-card" data-filter="in_progress" data-stat="in_progress">
                <div class="stat-icon text-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?= $stats['in_progress'] ?></div>
                <div class="stat-label">قيد التنفيذ</div>
            </div>

            <div class="stat-card" data-filter="pending" data-stat="pending">
                <div class="stat-icon text-secondary">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stat-number"><?= $stats['pending'] ?></div>
                <div class="stat-label">في الانتظار</div>
            </div>

            <div class="stat-card" data-filter="overdue" data-stat="overdue">
                <div class="stat-icon text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?= $stats['overdue'] ?></div>
                <div class="stat-label">متأخرة</div>
            </div>

            <div class="stat-card" data-filter="today" data-stat="today">
                <div class="stat-icon text-info">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-number"><?= $stats['today'] ?></div>
                <div class="stat-label">اليوم</div>
            </div>

            <div class="stat-card" data-stat="completion_rate">
                <div class="stat-icon text-primary">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="stat-number"><?= $stats['completion_rate'] ?>%</div>
                <div class="stat-label">معدل الإنجاز</div>
            </div>

            <div class="stat-card" data-stat="avg_progress">
                <div class="stat-icon text-info">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number"><?= $stats['avg_progress'] ?>%</div>
                <div class="stat-label">متوسط التقدم</div>
            </div>
        </section>

        <!-- شريط التنقل والفلاتر -->
        <nav class="navigation-bar animate__animated animate__fadeInUp">
            <!-- أزرار الإجراءات الرئيسية -->
            <div class="action-buttons mb-4">
                <?php if ($isAdmin): ?>
                    <button class="btn btn-primary" onclick="showAddTaskModal()">
                        <i class="fas fa-plus"></i> إضافة مهمة جديدة
                    </button>
                <?php endif; ?>

                <button class="btn btn-success" onclick="TaskManager.exportTasks('excel')">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>

                <button class="btn btn-info" onclick="TaskManager.exportTasks('pdf')">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>

                <button class="btn btn-secondary" onclick="TaskManager.printTasks()">
                    <i class="fas fa-print"></i> طباعة
                </button>

                <button class="btn btn-warning" onclick="TaskManager.refreshData()">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>

            <!-- نظام البحث والفلترة المتقدم -->
            <form id="filterForm" class="search-filters" method="GET">
                <div class="filter-group">
                    <label class="filter-label">البحث</label>
                    <input type="text"
                           id="searchInput"
                           name="search"
                           class="filter-input"
                           placeholder="ابحث في العنوان أو الوصف..."
                           value="<?= htmlspecialchars($filters['search']) ?>">
                </div>

                <div class="filter-group">
                    <label class="filter-label">الحالة</label>
                    <select name="status" class="filter-select">
                        <option value="">جميع الحالات</option>
                        <option value="لم تنفذ" <?= $filters['status'] === 'لم تنفذ' ? 'selected' : '' ?>>لم تنفذ</option>
                        <option value="تحت التنفيذ" <?= $filters['status'] === 'تحت التنفيذ' ? 'selected' : '' ?>>تحت التنفيذ</option>
                        <option value="تم" <?= $filters['status'] === 'تم' ? 'selected' : '' ?>>تم</option>
                        <option value="ملغي" <?= $filters['status'] === 'ملغي' ? 'selected' : '' ?>>ملغي</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">الأولوية</label>
                    <select name="priority" class="filter-select">
                        <option value="">جميع الأولويات</option>
                        <option value="منخفضة" <?= $filters['priority'] === 'منخفضة' ? 'selected' : '' ?>>منخفضة</option>
                        <option value="متوسطة" <?= $filters['priority'] === 'متوسطة' ? 'selected' : '' ?>>متوسطة</option>
                        <option value="عالية" <?= $filters['priority'] === 'عالية' ? 'selected' : '' ?>>عالية</option>
                        <option value="عاجلة" <?= $filters['priority'] === 'عاجلة' ? 'selected' : '' ?>>عاجلة</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">التصنيف</label>
                    <select name="category" class="filter-select">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= htmlspecialchars($category['Name']) ?>"
                                    <?= $filters['category'] === $category['Name'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['Name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">المسؤول</label>
                    <select name="assigned_to" class="filter-select">
                        <option value="">جميع المستخدمين</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?= $user['ID'] ?>"
                                    <?= $filters['assigned_to'] == $user['ID'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($user['FullName'] ?: $user['Username']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">من تاريخ</label>
                    <input type="date"
                           name="date_from"
                           class="filter-input"
                           value="<?= htmlspecialchars($filters['date_from']) ?>">
                </div>

                <div class="filter-group">
                    <label class="filter-label">إلى تاريخ</label>
                    <input type="date"
                           name="date_to"
                           class="filter-input"
                           value="<?= htmlspecialchars($filters['date_to']) ?>">
                </div>

                <div class="filter-group">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>

                <div class="filter-group">
                    <a href="?" class="btn btn-secondary w-100">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </a>
                </div>
            </form>
        </nav>

        <!-- جدول المهام -->
        <section class="tasks-table-container animate__animated animate__fadeInUp">
            <div class="table-responsive">
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'Title') ?>" class="text-white text-decoration-none">
                                    العنوان <?= getSortIcon($filters, 'Title') ?>
                                </a>
                            </th>
                            <th>الوصف</th>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'NotifyDate') ?>" class="text-white text-decoration-none">
                                    تاريخ البداية <?= getSortIcon($filters, 'NotifyDate') ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'ToDate') ?>" class="text-white text-decoration-none">
                                    تاريخ الانتهاء <?= getSortIcon($filters, 'ToDate') ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'Status') ?>" class="text-white text-decoration-none">
                                    الحالة <?= getSortIcon($filters, 'Status') ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'Priority') ?>" class="text-white text-decoration-none">
                                    الأولوية <?= getSortIcon($filters, 'Priority') ?>
                                </a>
                            </th>
                            <th>التصنيف</th>
                            <th>
                                <a href="<?= buildSortUrl($filters, 'Progress') ?>" class="text-white text-decoration-none">
                                    التقدم <?= getSortIcon($filters, 'Progress') ?>
                                </a>
                            </th>
                            <th>المسؤول</th>
                            <th>الأيام المتبقية</th>
                            <th>التعليقات</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($tasks)): ?>
                            <tr>
                                <td colspan="12" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <h5>لا توجد مهام</h5>
                                        <p>لم يتم العثور على مهام تطابق معايير البحث</p>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($tasks as $task): ?>
                                <tr class="task-row <?= $task['row_class'] ?>"
                                    data-task-id="<?= $task['ID'] ?>"
                                    data-is-today="<?= $task['row_class'] === 'today' ? 'true' : 'false' ?>"
                                    data-is-overdue="<?= $task['row_class'] === 'overdue' ? 'true' : 'false' ?>"
                                    data-start-date="<?= formatDate($task['NotifyDate']) ?>"
                                    data-end-date="<?= formatDate($task['ToDate']) ?>">

                                    <!-- العنوان -->
                                    <td class="task-title">
                                        <div class="d-flex align-items-center">
                                            <i class="<?= getStatusIcon($task['Status']) ?> me-2"
                                               style="color: <?= getStatusColor($task['Status']) ?>;"></i>
                                            <span title="<?= htmlspecialchars($task['Title']) ?>">
                                                <?= htmlspecialchars(truncateText($task['Title'], 40)) ?>
                                            </span>
                                        </div>
                                    </td>

                                    <!-- الوصف -->
                                    <td class="task-description">
                                        <span title="<?= htmlspecialchars(strip_tags($task['Message'])) ?>">
                                            <?= htmlspecialchars(truncateText(strip_tags($task['Message']), 60)) ?>
                                        </span>
                                    </td>

                                    <!-- تاريخ البداية -->
                                    <td><?= formatDate($task['NotifyDate']) ?></td>

                                    <!-- تاريخ الانتهاء -->
                                    <td><?= formatDate($task['ToDate']) ?></td>

                                    <!-- الحالة -->
                                    <td>
                                        <span class="status-badge <?= getStatusClass($task['Status']) ?>"
                                              data-status="<?= $task['Status'] ?>">
                                            <i class="<?= getStatusIcon($task['Status']) ?>"></i>
                                            <?= $task['Status'] ?>
                                        </span>
                                    </td>

                                    <!-- الأولوية -->
                                    <td>
                                        <span class="priority-badge <?= getPriorityClass($task['Priority']) ?>"
                                              data-priority="<?= $task['Priority'] ?>">
                                            <i class="<?= getPriorityIcon($task['Priority']) ?>"></i>
                                            <?= $task['Priority'] ?>
                                        </span>
                                    </td>

                                    <!-- التصنيف -->
                                    <td>
                                        <?php if ($task['Category']): ?>
                                            <span class="category-badge"
                                                  data-category="<?= $task['Category'] ?>"
                                                  style="background-color: <?= $task['CategoryColor'] ?? '#6c757d' ?>;">
                                                <i class="<?= $task['CategoryIcon'] ?? 'fas fa-folder' ?>"></i>
                                                <?= htmlspecialchars($task['Category']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>

                                    <!-- التقدم -->
                                    <td>
                                        <div class="progress-container">
                                            <div class="progress-bar <?= getProgressClass($task['Progress']) ?>"
                                                 style="width: <?= $task['Progress'] ?>%"></div>
                                        </div>
                                        <small class="text-muted"><?= $task['Progress'] ?>%</small>
                                    </td>

                                    <!-- المسؤول -->
                                    <td>
                                        <?= htmlspecialchars($task['AssignedToFullName'] ?: $task['AssignedToName'] ?: 'غير محدد') ?>
                                    </td>

                                    <!-- الأيام المتبقية -->
                                    <td>
                                        <small class="<?= $task['row_class'] === 'overdue' ? 'text-danger' : 'text-muted' ?>">
                                            <?= $task['days_remaining'] ?>
                                        </small>
                                    </td>

                                    <!-- التعليقات -->
                                    <td>
                                        <?php if ($task['comments_count'] > 0): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-comments"></i>
                                                <?= $task['comments_count'] ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>

                                    <!-- الإجراءات -->
                                    <td class="action-buttons no-print">
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($task['Status'] !== 'تم'): ?>
                                                <button class="btn btn-success action-btn"
                                                        data-action="complete"
                                                        title="إكمال">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>

                                            <?php if ($isAdmin): ?>
                                                <button class="btn btn-primary action-btn"
                                                        data-action="edit"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>

                                                <button class="btn btn-danger action-btn"
                                                        data-action="delete"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>

                                            <button class="btn btn-info action-btn"
                                                    data-action="view"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- التنقل بين الصفحات -->
        <?php if ($totalPages > 1): ?>
            <nav class="pagination-nav animate__animated animate__fadeInUp">
                <ul class="pagination justify-content-center">
                    <!-- الصفحة السابقة -->
                    <?php if ($filters['page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= buildFilterUrl($filters, ['page' => $filters['page'] - 1]) ?>">
                                <i class="fas fa-chevron-right"></i> السابق
                            </a>
                        </li>
                    <?php endif; ?>

                    <!-- أرقام الصفحات -->
                    <?php
                    $startPage = max(1, $filters['page'] - 2);
                    $endPage = min($totalPages, $filters['page'] + 2);

                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                        <li class="page-item <?= $i === $filters['page'] ? 'active' : '' ?>">
                            <a class="page-link" href="<?= buildFilterUrl($filters, ['page' => $i]) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <!-- الصفحة التالية -->
                    <?php if ($filters['page'] < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?= buildFilterUrl($filters, ['page' => $filters['page'] + 1]) ?>">
                                التالي <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <div class="text-center mt-3">
                    <small class="text-muted">
                        عرض <?= count($tasks) ?> من أصل <?= $totalTasks ?> مهمة
                        (الصفحة <?= $filters['page'] ?> من <?= $totalPages ?>)
                    </small>
                </div>
            </nav>
        <?php endif; ?>

    </div>

    <!-- مودال إضافة/تعديل المهمة -->
    <div id="taskModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إضافة مهمة جديدة</h3>
                <button type="button" class="modal-close">&times;</button>
            </div>

            <form id="taskForm" method="POST">
                <input type="hidden" name="task_id" id="taskId">

                <div class="form-group">
                    <label class="form-label">العنوان *</label>
                    <input type="text" name="title" id="taskTitle" class="form-input" required>
                </div>

                <div class="form-group">
                    <label class="form-label">الوصف</label>
                    <textarea name="message" id="taskMessage" class="form-textarea" rows="4"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تاريخ البداية *</label>
                            <input type="date" name="notify_date" id="taskNotifyDate" class="form-input" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تاريخ الانتهاء</label>
                            <input type="date" name="end_date" id="taskEndDate" class="form-input">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select name="status" id="taskStatus" class="form-select">
                                <option value="لم تنفذ">لم تنفذ</option>
                                <option value="تحت التنفيذ">تحت التنفيذ</option>
                                <option value="تم">تم</option>
                                <option value="ملغي">ملغي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">الأولوية</label>
                            <select name="priority" id="taskPriority" class="form-select">
                                <option value="منخفضة">منخفضة</option>
                                <option value="متوسطة" selected>متوسطة</option>
                                <option value="عالية">عالية</option>
                                <option value="عاجلة">عاجلة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">التصنيف</label>
                            <select name="category" id="taskCategory" class="form-select">
                                <option value="">اختر التصنيف</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category['Name']) ?>">
                                        <?= htmlspecialchars($category['Name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">المسؤول</label>
                            <select name="assigned_to" id="taskAssignedTo" class="form-select">
                                <option value="">اختر المسؤول</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['ID'] ?>">
                                        <?= htmlspecialchars($user['FullName'] ?: $user['Username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">التقدم (%)</label>
                    <input type="range" name="progress" id="taskProgress" class="form-range" min="0" max="100" value="0">
                    <div class="d-flex justify-content-between">
                        <small>0%</small>
                        <small id="progressValue">0%</small>
                        <small>100%</small>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" id="taskNotes" class="form-textarea" rows="3"></textarea>
                </div>

                <div class="d-flex gap-2 justify-content-end">
                    <button type="button" class="btn btn-secondary" onclick="TaskManager.closeModal()">
                        إلغاء
                    </button>
                    <button type="submit" name="add_task" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- مودال عرض تفاصيل المهمة -->
    <div id="taskDetailsModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تفاصيل المهمة</h3>
                <button type="button" class="modal-close">&times;</button>
            </div>

            <div id="taskDetailsContent">
                <!-- سيتم تحميل المحتوى ديناميكياً -->
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- JavaScript المخصص -->
    <script src="task-management-script.js"></script>

    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث قيمة شريط التقدم
            const progressSlider = document.getElementById('taskProgress');
            const progressValue = document.getElementById('progressValue');

            if (progressSlider && progressValue) {
                progressSlider.addEventListener('input', function() {
                    progressValue.textContent = this.value + '%';
                });
            }

            // تهيئة التنبيهات التلقائية
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        alert.classList.add('fade');
                    }
                });
            }, 5000);

            // تحديث الإحصائيات كل 30 ثانية
            setInterval(() => {
                if (typeof TaskManager !== 'undefined') {
                    TaskManager.updateStatsRealTime();
                }
            }, 30000);
        });

        // وظائف المودال
        function showAddTaskModal() {
            document.getElementById('taskModal').classList.add('active');
            document.getElementById('taskForm').reset();
            document.getElementById('taskId').value = '';
            document.querySelector('#taskModal .modal-title').textContent = 'إضافة مهمة جديدة';
            document.querySelector('#taskForm button[type="submit"]').name = 'add_task';
            document.body.style.overflow = 'hidden';
        }

        function showEditTaskModal(taskData) {
            document.getElementById('taskModal').classList.add('active');
            document.getElementById('taskId').value = taskData.ID;
            document.getElementById('taskTitle').value = taskData.Title;
            document.getElementById('taskMessage').value = taskData.Message;
            document.getElementById('taskNotifyDate').value = taskData.NotifyDate;
            document.getElementById('taskEndDate').value = taskData.ToDate;
            document.getElementById('taskStatus').value = taskData.Status;
            document.getElementById('taskPriority').value = taskData.Priority;
            document.getElementById('taskCategory').value = taskData.Category || '';
            document.getElementById('taskAssignedTo').value = taskData.AssignedTo || '';
            document.getElementById('taskProgress').value = taskData.Progress || 0;
            document.getElementById('taskNotes').value = taskData.Notes || '';

            document.querySelector('#taskModal .modal-title').textContent = 'تعديل المهمة';
            document.querySelector('#taskForm button[type="submit"]').name = 'update_task';
            document.body.style.overflow = 'hidden';

            // تحديث قيمة التقدم
            document.getElementById('progressValue').textContent = (taskData.Progress || 0) + '%';
        }

        // إغلاق المودال عند النقر خارجه
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                e.target.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // إغلاق المودال بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal-overlay.active');
                if (activeModal) {
                    activeModal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }
        });

        // تحسين تجربة المستخدم
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                const filter = this.dataset.filter;
                if (filter) {
                    // تطبيق الفلتر السريع
                    const url = new URL(window.location);
                    url.searchParams.set('status', filter === 'completed' ? 'تم' :
                                                  filter === 'in_progress' ? 'تحت التنفيذ' :
                                                  filter === 'pending' ? 'لم تنفذ' : '');
                    window.location.href = url.toString();
                }
            });
        });

        // تحسين الأداء - تحميل الصور بشكل تدريجي
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    </script>
</body>
</html>

<?php
/**
 * دالة مساعدة للحصول على لون الحالة
 */
function getStatusColor($status) {
    $colors = [
        'تم' => '#28a745',
        'تحت التنفيذ' => '#ffc107',
        'لم تنفذ' => '#6c757d',
        'ملغي' => '#dc3545'
    ];
    return $colors[$status] ?? '#6c757d';
}
?>
