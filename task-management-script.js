/**
 * نظام إدارة المهام المحسن - JavaScript
 * الإصدار: 4.0
 * التاريخ: 2025-07-14
 * المطور: Augment Agent
 */

'use strict';

// ===== المتغيرات العامة =====
const TaskManager = {
    // إعدادات النظام
    config: {
        debounceDelay: 300,
        animationDuration: 300,
        maxRetries: 3,
        cacheExpiry: 300000, // 5 دقائق
        pageSize: 50
    },

    // التخزين المؤقت
    cache: new Map(),

    // حالة التطبيق
    state: {
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        activeFilters: {},
        selectedTasks: new Set(),
        sortBy: 'NotifyDate',
        sortOrder: 'ASC'
    },

    // العناصر DOM
    elements: {
        searchInput: null,
        filterForm: null,
        tasksTable: null,
        statsCards: null,
        loadingOverlay: null,
        modal: null
    }
};

// ===== تهيئة النظام =====
document.addEventListener('DOMContentLoaded', function() {
    TaskManager.init();
});

TaskManager.init = function() {
    console.log('🚀 تهيئة نظام إدارة المهام المحسن...');

    // تهيئة العناصر
    this.initElements();

    // تهيئة الأحداث
    this.initEventListeners();

    // تحميل البيانات الأولية
    this.loadInitialData();

    // تهيئة الميزات المتقدمة
    this.initAdvancedFeatures();

    console.log('✅ تم تهيئة النظام بنجاح');
};

TaskManager.initElements = function() {
    this.elements = {
        searchInput: document.getElementById('searchInput'),
        filterForm: document.getElementById('filterForm'),
        tasksTable: document.querySelector('.tasks-table'),
        statsCards: document.querySelectorAll('.stat-card'),
        loadingOverlay: document.getElementById('loadingOverlay'),
        modal: document.getElementById('taskModal')
    };
};

TaskManager.initEventListeners = function() {
    // البحث الفوري
    if (this.elements.searchInput) {
        this.elements.searchInput.addEventListener('input',
            this.debounce(this.handleSearch.bind(this), this.config.debounceDelay)
        );
    }

    // فلترة النتائج
    if (this.elements.filterForm) {
        this.elements.filterForm.addEventListener('change', this.handleFilterChange.bind(this));
    }

    // النقر على بطاقات الإحصائيات
    this.elements.statsCards.forEach(card => {
        card.addEventListener('click', this.handleStatCardClick.bind(this));
    });

    // النقر على صفوف الجدول
    if (this.elements.tasksTable) {
        this.elements.tasksTable.addEventListener('click', this.handleTableClick.bind(this));
    }

    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

    // تحديث الصفحة
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
};

// ===== وظائف البحث والفلترة =====
TaskManager.handleSearch = function(event) {
    const query = event.target.value.trim();

    if (query.length < 2 && query.length > 0) {
        return; // تجاهل البحث القصير
    }

    this.state.activeFilters.search = query;
    this.filterTasks();
    this.updateResultsCount();
};

TaskManager.handleFilterChange = function(event) {
    const formData = new FormData(this.elements.filterForm);

    // تحديث الفلاتر النشطة
    this.state.activeFilters = {};
    for (let [key, value] of formData.entries()) {
        if (value) {
            this.state.activeFilters[key] = value;
        }
    }

    this.filterTasks();
    this.updateResultsCount();
    this.saveFiltersToStorage();
};

TaskManager.filterTasks = function() {
    if (!this.elements.tasksTable) return;

    const rows = this.elements.tasksTable.querySelectorAll('.task-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const isVisible = this.isRowMatchingFilters(row);
        row.style.display = isVisible ? '' : 'none';

        if (isVisible) {
            visibleCount++;
            row.classList.add('fade-in');
        }
    });

    this.updateResultsCount(visibleCount);
    this.updateEmptyState(visibleCount === 0);
};

TaskManager.isRowMatchingFilters = function(row) {
    const filters = this.state.activeFilters;

    // فلتر البحث
    if (filters.search) {
        const title = row.querySelector('.task-title')?.textContent.toLowerCase() || '';
        const description = row.querySelector('.task-description')?.textContent.toLowerCase() || '';
        const searchTerm = filters.search.toLowerCase();

        if (!title.includes(searchTerm) && !description.includes(searchTerm)) {
            return false;
        }
    }

    // فلتر الحالة
    if (filters.status) {
        const statusElement = row.querySelector('.status-badge');
        const currentStatus = statusElement?.dataset.status || '';
        if (currentStatus !== filters.status) {
            return false;
        }
    }

    // فلتر الأولوية
    if (filters.priority) {
        const priorityElement = row.querySelector('.priority-badge');
        const currentPriority = priorityElement?.dataset.priority || '';
        if (currentPriority !== filters.priority) {
            return false;
        }
    }

    // فلتر التصنيف
    if (filters.category) {
        const categoryElement = row.querySelector('.category-badge');
        const currentCategory = categoryElement?.dataset.category || '';
        if (currentCategory !== filters.category) {
            return false;
        }
    }

    return true;
};

// ===== وظائف الإحصائيات =====
TaskManager.handleStatCardClick = function(event) {
    const card = event.currentTarget;
    const filterType = card.dataset.filter;

    if (filterType) {
        this.applyQuickFilter(filterType);
        this.highlightActiveStatCard(card);
    }
};

TaskManager.applyQuickFilter = function(filterType) {
    // مسح الفلاتر الحالية
    this.clearFilters();

    switch (filterType) {
        case 'today':
            this.state.activeFilters.date_from = this.getCurrentDate();
            this.state.activeFilters.date_to = this.getCurrentDate();
            break;
        case 'overdue':
            this.state.activeFilters.status = 'overdue';
            break;
        case 'completed':
            this.state.activeFilters.status = 'تم';
            break;
        case 'in_progress':
            this.state.activeFilters.status = 'تحت التنفيذ';
            break;
        case 'pending':
            this.state.activeFilters.status = 'لم تنفذ';
            break;
    }

    this.updateFilterForm();
    this.filterTasks();
};

TaskManager.updateStatsRealTime = function() {
    if (!this.elements.tasksTable) return;

    const rows = this.elements.tasksTable.querySelectorAll('.task-row:not([style*="display: none"])');
    const stats = {
        total: rows.length,
        completed: 0,
        in_progress: 0,
        pending: 0,
        overdue: 0,
        today: 0
    };

    rows.forEach(row => {
        const status = row.querySelector('.status-badge')?.dataset.status || '';
        const isToday = row.dataset.isToday === 'true';
        const isOverdue = row.dataset.isOverdue === 'true';

        if (status === 'تم') stats.completed++;
        else if (status === 'تحت التنفيذ') stats.in_progress++;
        else if (status === 'لم تنفذ') stats.pending++;

        if (isOverdue) stats.overdue++;
        if (isToday) stats.today++;
    });

    // تحديث بطاقات الإحصائيات
    this.updateStatCards(stats);
};

TaskManager.updateStatCards = function(stats) {
    const statElements = {
        total: document.querySelector('[data-stat="total"] .stat-number'),
        completed: document.querySelector('[data-stat="completed"] .stat-number'),
        in_progress: document.querySelector('[data-stat="in_progress"] .stat-number'),
        pending: document.querySelector('[data-stat="pending"] .stat-number'),
        overdue: document.querySelector('[data-stat="overdue"] .stat-number'),
        today: document.querySelector('[data-stat="today"] .stat-number')
    };

    Object.keys(stats).forEach(key => {
        if (statElements[key]) {
            this.animateNumber(statElements[key], stats[key]);
        }
    });

    // تحديث معدل الإنجاز
    const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;
    const completionElement = document.querySelector('[data-stat="completion_rate"] .stat-number');
    if (completionElement) {
        this.animateNumber(completionElement, completionRate, '%');
    }
};

// ===== وظائف الجدول والتفاعل =====
TaskManager.handleTableClick = function(event) {
    const target = event.target;
    const row = target.closest('.task-row');

    if (!row) return;

    // النقر على زر الإجراءات
    if (target.classList.contains('action-btn')) {
        event.stopPropagation();
        this.handleActionButton(target, row);
        return;
    }

    // النقر على الصف لعرض التفاصيل
    if (target.closest('.task-title') || target.closest('.task-description')) {
        this.showTaskDetails(row.dataset.taskId);
    }
};

// ===== وظائف متقدمة إضافية =====
TaskManager.initAdvancedFeatures = function() {
    // تهيئة التحميل التدريجي
    this.initInfiniteScroll();

    // تهيئة اختصارات لوحة المفاتيح
    this.initKeyboardShortcuts();

    // تهيئة التحديث التلقائي للإحصائيات
    this.initAutoRefresh();

    // تحميل الفلاتر المحفوظة
    this.loadFiltersFromStorage();

    // تهيئة Service Worker للتخزين المؤقت
    this.initServiceWorker();
};

TaskManager.initInfiniteScroll = function() {
    const tableContainer = document.querySelector('.table-responsive');
    if (!tableContainer) return;

    tableContainer.addEventListener('scroll', this.debounce(() => {
        const { scrollTop, scrollHeight, clientHeight } = tableContainer;

        if (scrollTop + clientHeight >= scrollHeight - 100) {
            this.loadMoreTasks();
        }
    }, 100));
};

TaskManager.loadMoreTasks = function() {
    if (this.state.isLoading || this.state.currentPage >= this.state.totalPages) {
        return;
    }

    this.state.currentPage++;
    this.loadTasks(this.state.currentPage, true); // append = true
};

TaskManager.loadTasks = function(page = 1, append = false) {
    this.showLoading();

    const params = new URLSearchParams({
        page: page,
        ...this.state.activeFilters
    });

    fetch(`load_tasks.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (append) {
                    this.appendTasksToTable(data.tasks);
                } else {
                    this.replaceTasksInTable(data.tasks);
                }

                this.state.totalPages = data.totalPages;
                this.updateStatsRealTime();
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المهام:', error);
            this.showAlert('حدث خطأ في تحميل المهام', 'danger');
        })
        .finally(() => {
            this.hideLoading();
        });
};

TaskManager.handleKeyboardShortcuts = function(event) {
    // تجاهل الاختصارات إذا كان المستخدم يكتب في حقل
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
            case 'f':
                event.preventDefault();
                this.focusSearchInput();
                break;
            case 'n':
                event.preventDefault();
                this.showAddTaskModal();
                break;
            case 'r':
                event.preventDefault();
                this.refreshData();
                break;
        }
    }

    if (event.ctrlKey && event.shiftKey) {
        switch (event.key.toLowerCase()) {
            case 't':
                event.preventDefault();
                this.applyQuickFilter('today');
                break;
            case 'c':
                event.preventDefault();
                this.applyQuickFilter('completed');
                break;
            case 'p':
                event.preventDefault();
                this.applyQuickFilter('pending');
                break;
        }
    }

    // مفاتيح أخرى
    switch (event.key) {
        case 'Escape':
            this.closeModal();
            this.clearSelection();
            break;
    }
};

TaskManager.initAutoRefresh = function() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(() => {
        if (!this.state.isLoading) {
            this.updateStatsRealTime();
        }
    }, 30000);

    // تحديث البيانات كل 5 دقائق
    setInterval(() => {
        if (!this.state.isLoading) {
            this.refreshData(true); // silent refresh
        }
    }, 300000);
};

TaskManager.initServiceWorker = function() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker مسجل بنجاح');
            })
            .catch(error => {
                console.log('فشل في تسجيل Service Worker:', error);
            });
    }
};

// ===== وظائف إدارة المهام =====
TaskManager.updateTaskStatus = function(taskId, newStatus) {
    this.showLoading();

    const formData = new FormData();
    formData.append('task_id', taskId);
    formData.append('status', newStatus);
    formData.append('action', 'update_status');

    fetch('update_task.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            this.updateTaskRowStatus(taskId, newStatus);
            this.updateStatsRealTime();
            this.showAlert('تم تحديث حالة المهمة بنجاح', 'success');
        } else {
            this.showAlert(data.message || 'حدث خطأ في تحديث المهمة', 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        this.showAlert('حدث خطأ في الاتصال', 'danger');
    })
    .finally(() => {
        this.hideLoading();
    });
};

TaskManager.updateTaskRowStatus = function(taskId, newStatus) {
    const row = document.querySelector(`[data-task-id="${taskId}"]`);
    if (!row) return;

    const statusBadge = row.querySelector('.status-badge');
    if (statusBadge) {
        // تحديث النص والكلاس
        statusBadge.textContent = newStatus;
        statusBadge.className = `status-badge ${this.getStatusClass(newStatus)}`;
        statusBadge.dataset.status = newStatus;

        // إضافة تأثير بصري
        statusBadge.classList.add('slide-up');
        setTimeout(() => {
            statusBadge.classList.remove('slide-up');
        }, 300);
    }

    // تحديث كلاس الصف
    row.className = `task-row ${this.getRowClass(newStatus)}`;
};

TaskManager.deleteTask = function(taskId) {
    if (!confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
        return;
    }

    this.showLoading();

    const formData = new FormData();
    formData.append('task_id', taskId);
    formData.append('action', 'delete');

    fetch('delete_task.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            this.removeTaskRow(taskId);
            this.updateStatsRealTime();
            this.showAlert('تم حذف المهمة بنجاح', 'success');
        } else {
            this.showAlert(data.message || 'حدث خطأ في حذف المهمة', 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        this.showAlert('حدث خطأ في الاتصال', 'danger');
    })
    .finally(() => {
        this.hideLoading();
    });
};

TaskManager.removeTaskRow = function(taskId) {
    const row = document.querySelector(`[data-task-id="${taskId}"]`);
    if (row) {
        row.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            row.remove();
        }, 300);
    }
};

// ===== وظائف التصدير والطباعة =====
TaskManager.exportTasks = function(format = 'excel') {
    const params = new URLSearchParams({
        type: format,
        ...this.state.activeFilters
    });

    const exportUrl = `export_tasks.php?${params}`;

    // إنشاء رابط تحميل مخفي
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `tasks_${format}_${new Date().toISOString().split('T')[0]}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    this.showAlert(`جاري تحضير ملف ${format.toUpperCase()}...`, 'info');
};

TaskManager.printTasks = function() {
    const printWindow = window.open('', '_blank');
    const printContent = this.generatePrintHTML();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
};

TaskManager.generatePrintHTML = function() {
    const visibleRows = document.querySelectorAll('.task-row:not([style*="display: none"])');
    let tableHTML = '<table border="1" style="width:100%; border-collapse: collapse;">';

    // رأس الجدول
    tableHTML += `
        <thead>
            <tr style="background: #007bff; color: white;">
                <th style="padding: 10px;">العنوان</th>
                <th style="padding: 10px;">الوصف</th>
                <th style="padding: 10px;">تاريخ البداية</th>
                <th style="padding: 10px;">تاريخ الانتهاء</th>
                <th style="padding: 10px;">الحالة</th>
                <th style="padding: 10px;">الأولوية</th>
            </tr>
        </thead>
        <tbody>
    `;

    // صفوف البيانات
    visibleRows.forEach(row => {
        const title = row.querySelector('.task-title')?.textContent || '';
        const description = row.querySelector('.task-description')?.textContent || '';
        const startDate = row.dataset.startDate || '';
        const endDate = row.dataset.endDate || '';
        const status = row.querySelector('.status-badge')?.textContent || '';
        const priority = row.querySelector('.priority-badge')?.textContent || '';

        tableHTML += `
            <tr>
                <td style="padding: 8px;">${title}</td>
                <td style="padding: 8px;">${description}</td>
                <td style="padding: 8px;">${startDate}</td>
                <td style="padding: 8px;">${endDate}</td>
                <td style="padding: 8px;">${status}</td>
                <td style="padding: 8px;">${priority}</td>
            </tr>
        `;
    });

    tableHTML += '</tbody></table>';

    return `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المهام</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { text-align: center; color: #007bff; }
                table { margin-top: 20px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <h1>تقرير المهام - ${new Date().toLocaleDateString('ar-SA')}</h1>
            ${tableHTML}
        </body>
        </html>
    `;
};

// ===== وظائف مساعدة إضافية =====
TaskManager.getStatusClass = function(status) {
    const statusClasses = {
        'تم': 'status-completed',
        'تحت التنفيذ': 'status-in-progress',
        'لم تنفذ': 'status-pending'
    };
    return statusClasses[status] || 'status-pending';
};

TaskManager.getRowClass = function(status) {
    const rowClasses = {
        'تم': 'completed',
        'تحت التنفيذ': 'in-progress',
        'لم تنفذ': 'pending'
    };
    return rowClasses[status] || 'normal';
};

TaskManager.focusSearchInput = function() {
    if (this.elements.searchInput) {
        this.elements.searchInput.focus();
        this.elements.searchInput.select();
    }
};

TaskManager.clearFilters = function() {
    this.state.activeFilters = {};
    this.updateFilterForm();
    this.filterTasks();
    this.clearActiveStatCards();
};

TaskManager.clearActiveStatCards = function() {
    this.elements.statsCards.forEach(card => {
        card.classList.remove('active');
    });
};

TaskManager.highlightActiveStatCard = function(activeCard) {
    this.clearActiveStatCards();
    activeCard.classList.add('active');
};

TaskManager.updateFilterForm = function() {
    if (!this.elements.filterForm) return;

    const formElements = this.elements.filterForm.elements;

    Object.keys(this.state.activeFilters).forEach(key => {
        if (formElements[key]) {
            formElements[key].value = this.state.activeFilters[key];
        }
    });
};

TaskManager.updateResultsCount = function(count = null) {
    if (count === null) {
        const visibleRows = document.querySelectorAll('.task-row:not([style*="display: none"])');
        count = visibleRows.length;
    }

    let resultDiv = document.getElementById('searchResults');
    if (!resultDiv) {
        resultDiv = document.createElement('div');
        resultDiv.id = 'searchResults';
        resultDiv.className = 'alert alert-info mt-2';

        const container = document.querySelector('.search-filters');
        if (container) {
            container.appendChild(resultDiv);
        }
    }

    if (count === 0) {
        resultDiv.innerHTML = '<i class="fas fa-search"></i> لم يتم العثور على نتائج';
        resultDiv.className = 'alert alert-warning mt-2';
    } else {
        resultDiv.innerHTML = `<i class="fas fa-check"></i> عرض ${count} مهمة`;
        resultDiv.className = 'alert alert-info mt-2';
    }
};

TaskManager.updateEmptyState = function(isEmpty) {
    let emptyState = document.getElementById('emptyState');

    if (isEmpty) {
        if (!emptyState) {
            emptyState = document.createElement('div');
            emptyState.id = 'emptyState';
            emptyState.className = 'text-center py-5';
            emptyState.innerHTML = `
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مهام تطابق البحث</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
            `;

            const tableContainer = document.querySelector('.tasks-table-container');
            if (tableContainer) {
                tableContainer.appendChild(emptyState);
            }
        }
        emptyState.style.display = 'block';
    } else {
        if (emptyState) {
            emptyState.style.display = 'none';
        }
    }
};

TaskManager.refreshData = function(silent = false) {
    if (!silent) {
        this.showLoading();
    }

    this.loadTasks(1, false);
    this.updateStatsRealTime();

    if (!silent) {
        this.showAlert('تم تحديث البيانات', 'success');
    }
};

TaskManager.loadInitialData = function() {
    this.updateStatsRealTime();
    this.filterTasks();
};

TaskManager.handleBeforeUnload = function() {
    // حفظ حالة التطبيق قبل إغلاق الصفحة
    this.saveFiltersToStorage();
};

// إضافة CSS للأنيميشن
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; transform: scale(1); }
        to { opacity: 0; transform: scale(0.95); }
    }

    .stat-card.active {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 123, 255, 0.2);
        border: 2px solid var(--primary-color);
    }
`;
document.head.appendChild(style);
        this.handleActionButton(target, row);
        return;
    }

    // النقر على الصف لعرض التفاصيل
    if (target.closest('.task-title') || target.closest('.task-description')) {
        this.showTaskDetails(row.dataset.taskId);
    }
};

TaskManager.handleActionButton = function(button, row) {
    const action = button.dataset.action;
    const taskId = row.dataset.taskId;

    switch (action) {
        case 'edit':
            this.editTask(taskId);
            break;
        case 'delete':
            this.deleteTask(taskId);
            break;
        case 'complete':
            this.updateTaskStatus(taskId, 'تم');
            break;
        case 'start':
            this.updateTaskStatus(taskId, 'تحت التنفيذ');
            break;
    }
};

// ===== وظائف المودال والنماذج =====
TaskManager.showTaskDetails = function(taskId) {
    this.showLoading();

    fetch(`get_task_details.php?id=${taskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.displayTaskModal(data.task);
            } else {
                this.showAlert('خطأ في تحميل تفاصيل المهمة', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            this.showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            this.hideLoading();
        });
};

TaskManager.displayTaskModal = function(task) {
    if (!this.elements.modal) return;

    // تحديث محتوى المودال
    const modalContent = this.elements.modal.querySelector('.modal-content');
    modalContent.innerHTML = this.generateTaskModalHTML(task);

    // إظهار المودال
    this.elements.modal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // إضافة مستمع الإغلاق
    const closeBtn = modalContent.querySelector('.modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', this.closeModal.bind(this));
    }
};

TaskManager.closeModal = function() {
    if (this.elements.modal) {
        this.elements.modal.classList.remove('active');
        document.body.style.overflow = '';
    }
};

// ===== وظائف مساعدة =====
TaskManager.debounce = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

TaskManager.animateNumber = function(element, targetValue, suffix = '') {
    if (!element) return;

    const startValue = parseInt(element.textContent) || 0;
    const increment = (targetValue - startValue) / 30;
    let currentValue = startValue;

    const timer = setInterval(() => {
        currentValue += increment;
        if ((increment > 0 && currentValue >= targetValue) ||
            (increment < 0 && currentValue <= targetValue)) {
            element.textContent = targetValue + suffix;
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(currentValue) + suffix;
        }
    }, 16);
};

TaskManager.showLoading = function() {
    if (this.elements.loadingOverlay) {
        this.elements.loadingOverlay.classList.remove('hidden');
    }
    this.state.isLoading = true;
};

TaskManager.hideLoading = function() {
    if (this.elements.loadingOverlay) {
        this.elements.loadingOverlay.classList.add('hidden');
    }
    this.state.isLoading = false;
};

TaskManager.showAlert = function(message, type = 'info') {
    const alertHTML = `
        <div class="alert alert-${type} fade-in">
            <i class="fas fa-${this.getAlertIcon(type)}"></i>
            ${message}
        </div>
    `;

    const container = document.querySelector('.task-management-container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHTML);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
};

TaskManager.getAlertIcon = function(type) {
    const icons = {
        success: 'check-circle',
        danger: 'exclamation-triangle',
        warning: 'exclamation-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
};

TaskManager.getCurrentDate = function() {
    return new Date().toISOString().split('T')[0];
};

TaskManager.saveFiltersToStorage = function() {
    localStorage.setItem('taskFilters', JSON.stringify(this.state.activeFilters));
};

TaskManager.loadFiltersFromStorage = function() {
    const saved = localStorage.getItem('taskFilters');
    if (saved) {
        this.state.activeFilters = JSON.parse(saved);
        this.updateFilterForm();
    }
};

// ===== تصدير الكائن للاستخدام العام =====
window.TaskManager = TaskManager;
